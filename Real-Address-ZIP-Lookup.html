<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌍 真实地址邮政编码查询工具 - 免费API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .search-form {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row.full {
            grid-template-columns: 1fr;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
            font-size: 1.1em;
        }

        .form-group input,
        .form-group select {
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }

        .search-btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            width: 100%;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
        }

        .search-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .api-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .api-option {
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .api-option:hover {
            border-color: #4CAF50;
            transform: translateY(-2px);
        }

        .api-option.selected {
            border-color: #4CAF50;
            background: #e8f5e8;
        }

        .api-option h4 {
            color: #333;
            margin-bottom: 5px;
        }

        .api-option p {
            color: #666;
            font-size: 0.9em;
        }

        .results-section {
            background: white;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            overflow: hidden;
        }

        .results-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .results-header h3 {
            color: #333;
            font-size: 1.5em;
        }

        .results-content {
            padding: 20px;
        }

        .result-item {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 5px solid #4CAF50;
        }

        .zip-code {
            font-size: 2em;
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 10px;
        }

        .address-info {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .btn-copy {
            background: #2196F3;
            color: white;
        }

        .btn-copy:hover {
            background: #1976D2;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 1.2em;
        }

        .error {
            background: #ffebee;
            color: #c62828;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #f44336;
            margin: 20px 0;
        }

        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #4CAF50;
            margin: 20px 0;
        }

        .info-box {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 5px solid #2196F3;
        }

        .info-box h4 {
            color: #1976D2;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }

            .api-selector {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌍 真实地址邮政编码查询</h1>
            <p>使用免费API获取真实准确的邮政编码信息</p>
        </div>

        <div class="main-content">
            <div class="search-form">
                <h3 style="margin-bottom: 20px; color: #333;">🔧 选择查询API</h3>
                <div class="api-selector">
                    <div class="api-option selected" onclick="selectAPI('nominatim')" id="api-nominatim">
                        <h4>🌍 Nominatim (OSM)</h4>
                        <p>完全免费，无需API密钥</p>
                    </div>
                    <div class="api-option" onclick="selectAPI('geocoding')" id="api-geocoding">
                        <h4>🗺️ Geocoding API</h4>
                        <p>免费额度：1000次/天</p>
                    </div>
                    <div class="api-option" onclick="selectAPI('mapbox')" id="api-mapbox">
                        <h4>📍 MapBox</h4>
                        <p>免费额度：100,000次/月</p>
                    </div>
                </div>

                <div class="form-row full">
                    <div class="form-group">
                        <label for="streetAddress">🏠 街道地址</label>
                        <input type="text" id="streetAddress" placeholder="例如: 1600 Pennsylvania Avenue NW">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="city">🏙️ 城市</label>
                        <input type="text" id="city" placeholder="例如: Washington">
                    </div>
                    
                    <div class="form-group">
                        <label for="state">🏛️ 州</label>
                        <select id="state">
                            <option value="">-- 选择州 --</option>
                        </select>
                    </div>
                </div>

                <!-- API密钥配置区域 -->
                <div id="apiKeySection" style="display: none;">
                    <div class="form-row full">
                        <div class="form-group">
                            <label for="apiKey">🔑 API密钥</label>
                            <input type="password" id="apiKey" placeholder="输入API密钥...">
                        </div>
                    </div>
                </div>
                
                <button class="search-btn" onclick="searchZipCode()">
                    🔍 查找邮政编码
                </button>
            </div>

            <div class="results-section">
                <div class="results-header">
                    <h3>📊 查询结果</h3>
                </div>
                <div class="results-content" id="resultsContent">
                    <div class="loading">
                        👋 欢迎使用真实地址邮政编码查询工具！<br>
                        选择API并输入地址信息开始查询
                    </div>
                </div>
            </div>

            <div class="info-box">
                <h4>💡 免费API说明</h4>
                <p><strong>🌍 Nominatim (推荐):</strong> OpenStreetMap提供的完全免费地理编码服务，无需注册，无使用限制。</p>
                <p><strong>🗺️ Geocoding API:</strong> 需要注册获取免费API密钥，每天1000次免费查询。</p>
                <p><strong>📍 MapBox:</strong> 需要注册获取API密钥，每月100,000次免费查询。</p>
                <br>
                <p><strong>⚠️ 注意:</strong> 免费API可能有查询频率限制，建议合理使用。</p>
            </div>
        </div>
    </div>

    <script>
        // 🗺️ 美国所有州
        const US_STATES = {
            'Alabama': 'AL', 'Alaska': 'AK', 'Arizona': 'AZ', 'Arkansas': 'AR', 'California': 'CA',
            'Colorado': 'CO', 'Connecticut': 'CT', 'Delaware': 'DE', 'Florida': 'FL', 'Georgia': 'GA',
            'Hawaii': 'HI', 'Idaho': 'ID', 'Illinois': 'IL', 'Indiana': 'IN', 'Iowa': 'IA',
            'Kansas': 'KS', 'Kentucky': 'KY', 'Louisiana': 'LA', 'Maine': 'ME', 'Maryland': 'MD',
            'Massachusetts': 'MA', 'Michigan': 'MI', 'Minnesota': 'MN', 'Mississippi': 'MS', 'Missouri': 'MO',
            'Montana': 'MT', 'Nebraska': 'NE', 'Nevada': 'NV', 'New Hampshire': 'NH', 'New Jersey': 'NJ',
            'New Mexico': 'NM', 'New York': 'NY', 'North Carolina': 'NC', 'North Dakota': 'ND', 'Ohio': 'OH',
            'Oklahoma': 'OK', 'Oregon': 'OR', 'Pennsylvania': 'PA', 'Rhode Island': 'RI', 'South Carolina': 'SC',
            'South Dakota': 'SD', 'Tennessee': 'TN', 'Texas': 'TX', 'Utah': 'UT', 'Vermont': 'VT',
            'Virginia': 'VA', 'Washington': 'WA', 'West Virginia': 'WV', 'Wisconsin': 'WI', 'Wyoming': 'WY',
            'District of Columbia': 'DC'
        };

        let selectedAPI = 'nominatim';

        // 🚀 初始化页面
        function initializePage() {
            const stateSelect = document.getElementById('state');
            
            // 填充州选择器
            Object.keys(US_STATES).sort().forEach(stateName => {
                const option = document.createElement('option');
                option.value = US_STATES[stateName];
                option.textContent = `${stateName} (${US_STATES[stateName]})`;
                stateSelect.appendChild(option);
            });
        }

        // 🔧 选择API
        function selectAPI(apiType) {
            // 移除所有选中状态
            document.querySelectorAll('.api-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // 选中当前API
            document.getElementById(`api-${apiType}`).classList.add('selected');
            selectedAPI = apiType;
            
            // 显示/隐藏API密钥输入框
            const apiKeySection = document.getElementById('apiKeySection');
            if (apiType === 'nominatim') {
                apiKeySection.style.display = 'none';
            } else {
                apiKeySection.style.display = 'block';
                const apiKeyInput = document.getElementById('apiKey');
                apiKeyInput.placeholder = apiType === 'geocoding' ? 
                    '输入Geocoding API密钥...' : 
                    '输入MapBox访问令牌...';
            }
        }

        // 🔍 搜索邮政编码
        async function searchZipCode() {
            const streetAddress = document.getElementById('streetAddress').value.trim();
            const city = document.getElementById('city').value.trim();
            const state = document.getElementById('state').value;
            const apiKey = document.getElementById('apiKey').value.trim();

            if (!streetAddress || !city || !state) {
                showError('请填写完整的地址信息（街道地址、城市、州）');
                return;
            }

            if (selectedAPI !== 'nominatim' && !apiKey) {
                showError(`请输入${selectedAPI === 'geocoding' ? 'Geocoding API' : 'MapBox'}密钥`);
                return;
            }

            showLoading();

            try {
                let result;
                const fullAddress = `${streetAddress}, ${city}, ${state}, USA`;
                
                switch (selectedAPI) {
                    case 'nominatim':
                        result = await queryNominatim(fullAddress);
                        break;
                    case 'geocoding':
                        result = await queryGeocodingAPI(fullAddress, apiKey);
                        break;
                    case 'mapbox':
                        result = await queryMapBox(fullAddress, apiKey);
                        break;
                }
                
                if (result) {
                    showResults(result);
                } else {
                    showError('未找到该地址的邮政编码信息');
                }
            } catch (error) {
                showError(`查询失败: ${error.message}`);
            }
        }

        // 🌍 使用Nominatim API查询
        async function queryNominatim(address) {
            const encodedAddress = encodeURIComponent(address);
            const url = `https://nominatim.openstreetmap.org/search?format=json&addressdetails=1&limit=1&q=${encodedAddress}`;
            
            const response = await fetch(url, {
                headers: {
                    'User-Agent': 'Address-ZIP-Lookup-Tool/1.0'
                }
            });

            if (!response.ok) {
                throw new Error(`Nominatim API错误: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.length === 0) {
                return null;
            }

            const result = data[0];
            const addressDetails = result.address || {};
            
            return {
                zipCode: addressDetails.postcode || 'N/A',
                address: result.display_name,
                source: '🌍 Nominatim (OpenStreetMap)',
                latitude: result.lat,
                longitude: result.lon,
                confidence: result.importance,
                additionalInfo: `坐标: ${result.lat}, ${result.lon}\n置信度: ${(result.importance * 100).toFixed(1)}%`,
                rawData: result
            };
        }

        // 🗺️ 使用Geocoding API查询
        async function queryGeocodingAPI(address, apiKey) {
            const encodedAddress = encodeURIComponent(address);
            const url = `https://api.geocoding.dev/v1?api_key=${apiKey}&text=${encodedAddress}`;
            
            const response = await fetch(url);

            if (!response.ok) {
                throw new Error(`Geocoding API错误: ${response.status}`);
            }

            const data = await response.json();
            
            if (!data.features || data.features.length === 0) {
                return null;
            }

            const result = data.features[0];
            const properties = result.properties;
            
            return {
                zipCode: properties.postcode || 'N/A',
                address: properties.label,
                source: '🗺️ Geocoding API',
                latitude: result.geometry.coordinates[1],
                longitude: result.geometry.coordinates[0],
                confidence: properties.confidence,
                additionalInfo: `坐标: ${result.geometry.coordinates[1]}, ${result.geometry.coordinates[0]}\n置信度: ${properties.confidence}`,
                rawData: result
            };
        }

        // 📍 使用MapBox API查询
        async function queryMapBox(address, accessToken) {
            const encodedAddress = encodeURIComponent(address);
            const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodedAddress}.json?access_token=${accessToken}&country=US&types=address`;
            
            const response = await fetch(url);

            if (!response.ok) {
                throw new Error(`MapBox API错误: ${response.status}`);
            }

            const data = await response.json();
            
            if (!data.features || data.features.length === 0) {
                return null;
            }

            const result = data.features[0];
            const context = result.context || [];
            const postcode = context.find(c => c.id.startsWith('postcode'));
            
            return {
                zipCode: postcode ? postcode.text : 'N/A',
                address: result.place_name,
                source: '📍 MapBox',
                latitude: result.center[1],
                longitude: result.center[0],
                confidence: result.relevance,
                additionalInfo: `坐标: ${result.center[1]}, ${result.center[0]}\n相关性: ${(result.relevance * 100).toFixed(1)}%`,
                rawData: result
            };
        }

        // 📊 显示查询结果
        function showResults(data) {
            const resultsContent = document.getElementById('resultsContent');
            
            resultsContent.innerHTML = `
                <div class="result-item">
                    <div class="zip-code">📮 ${data.zipCode}</div>
                    <div class="address-info">
                        <strong>📍 地址:</strong> ${data.address}<br>
                        <strong>📊 数据源:</strong> <span style="font-weight: bold;">${data.source}</span><br>
                        <strong>🌐 坐标:</strong> ${data.latitude}, ${data.longitude}<br>
                        <strong>📈 置信度:</strong> ${data.confidence ? (typeof data.confidence === 'number' ? (data.confidence * 100).toFixed(1) + '%' : data.confidence) : 'N/A'}
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-copy" onclick="copyToClipboard('${data.zipCode}')">
                            📋 复制邮政编码
                        </button>
                        <button class="btn btn-copy" onclick="copyToClipboard('${data.address}')">
                            📋 复制完整地址
                        </button>
                        <button class="btn btn-copy" onclick="copyToClipboard('${data.latitude}, ${data.longitude}')">
                            📋 复制坐标
                        </button>
                        <button class="btn btn-copy" onclick="openInMaps('${data.latitude}', '${data.longitude}')">
                            🗺️ 在地图中查看
                        </button>
                        <button class="btn btn-copy" onclick="showRawData('${JSON.stringify(data.rawData).replace(/'/g, "\\'")}')">
                            📄 查看原始数据
                        </button>
                    </div>
                    ${data.additionalInfo ? `
                        <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px; font-size: 0.9em;">
                            <strong>ℹ️ 附加信息:</strong><br>
                            ${data.additionalInfo.replace(/\n/g, '<br>')}
                        </div>
                    ` : ''}
                    <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-radius: 5px; font-size: 0.9em; border-left: 4px solid #4CAF50;">
                        <strong>✅ 真实数据:</strong> 此结果来自真实的地理编码API，确保数据准确性。
                    </div>
                </div>
            `;
        }

        // 🗺️ 在地图中打开
        function openInMaps(lat, lng) {
            const url = `https://www.google.com/maps?q=${lat},${lng}`;
            window.open(url, '_blank');
        }

        // 📄 显示原始数据
        function showRawData(rawDataStr) {
            const rawData = JSON.parse(rawDataStr);
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex; 
                align-items: center; justify-content: center; padding: 20px;
            `;
            
            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 30px; max-width: 800px; width: 100%; max-height: 80%; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3>📄 API原始数据</h3>
                        <button onclick="this.closest('div').parentElement.remove()" 
                                style="background: #f44336; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer;">×</button>
                    </div>
                    <pre style="background: #f8f9fa; padding: 15px; border-radius: 8px; overflow-x: auto; font-size: 0.9em; white-space: pre-wrap;">${JSON.stringify(rawData, null, 2)}</pre>
                    <div style="margin-top: 15px;">
                        <button onclick="copyToClipboard('${rawDataStr.replace(/'/g, "\\'")}'); alert('原始数据已复制！');" 
                                style="background: #4CAF50; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                            📋 复制JSON数据
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }

        // 📋 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                const toast = document.createElement('div');
                toast.textContent = `✅ 已复制: ${text}`;
                toast.style.cssText = `
                    position: fixed; top: 20px; right: 20px; background: #4CAF50; 
                    color: white; padding: 10px 20px; border-radius: 5px; z-index: 1000;
                    animation: fadeInOut 3s ease-in-out;
                `;
                document.body.appendChild(toast);
                setTimeout(() => document.body.removeChild(toast), 3000);
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            });
        }

        // ⏳ 显示加载状态
        function showLoading() {
            document.getElementById('resultsContent').innerHTML = `
                <div class="loading">
                    🔄 正在查询邮政编码，请稍候...
                </div>
            `;
        }

        // ❌ 显示错误信息
        function showError(message) {
            document.getElementById('resultsContent').innerHTML = `
                <div class="error">
                    ❌ ${message}
                </div>
            `;
        }

        // 🚀 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>

    <style>
        @keyframes fadeInOut {
            0% { opacity: 0; transform: translateY(-20px); }
            20% { opacity: 1; transform: translateY(0); }
            80% { opacity: 1; transform: translateY(0); }
            100% { opacity: 0; transform: translateY(-20px); }
        }
    </style>
</body>
</html>
