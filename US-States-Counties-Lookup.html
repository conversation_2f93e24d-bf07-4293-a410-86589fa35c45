<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇺🇸 美国州县查询工具 - 支持所有50州</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .search-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
        }

        .search-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
            font-size: 1.1em;
        }

        .form-group select,
        .form-group input {
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }

        .search-btn {
            grid-column: span 3;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
        }

        .search-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .results-section {
            background: white;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            overflow: hidden;
        }

        .results-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .results-header h3 {
            color: #333;
            font-size: 1.5em;
        }

        .results-content {
            padding: 20px;
            max-height: 500px;
            overflow-y: auto;
        }

        .state-info {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 5px solid #4CAF50;
        }

        .state-info h4 {
            color: #2e7d32;
            font-size: 1.3em;
            margin-bottom: 10px;
        }

        .counties-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .county-item {
            background: white;
            padding: 10px 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .county-item:hover {
            background: #f0f8f0;
            border-color: #4CAF50;
            transform: translateY(-1px);
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 1.2em;
        }

        .error {
            background: #ffebee;
            color: #c62828;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #f44336;
            margin: 20px 0;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4CAF50;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .search-grid {
                grid-template-columns: 1fr;
            }
            
            .search-btn {
                grid-column: span 1;
            }
            
            .counties-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇺🇸 美国州县查询工具</h1>
            <p>支持所有50个州 + DC + 领土，实时获取最新地理数据</p>
        </div>

        <div class="main-content">
            <div class="search-section">
                <div class="search-grid">
                    <div class="form-group">
                        <label for="stateSelect">🏛️ 选择州</label>
                        <select id="stateSelect">
                            <option value="">-- 选择州 --</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="countyFilter">🔍 县名筛选</label>
                        <input type="text" id="countyFilter" placeholder="输入县名进行筛选...">
                    </div>
                    
                    <div class="form-group">
                        <label for="zipCode">📮 邮政编码</label>
                        <input type="text" id="zipCode" placeholder="输入邮政编码查询...">
                    </div>
                    
                    <button class="search-btn" onclick="searchLocation()">
                        🔍 查询地理信息
                    </button>
                </div>
            </div>

            <div class="results-section">
                <div class="results-header">
                    <h3>📊 查询结果</h3>
                </div>
                <div class="results-content" id="resultsContent">
                    <div class="loading">
                        👋 欢迎使用美国州县查询工具！<br>
                        请选择州开始查询
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 🗺️ 美国所有州和领土
        const US_STATES = {
            'Alabama': 'AL', 'Alaska': 'AK', 'Arizona': 'AZ', 'Arkansas': 'AR', 'California': 'CA',
            'Colorado': 'CO', 'Connecticut': 'CT', 'Delaware': 'DE', 'Florida': 'FL', 'Georgia': 'GA',
            'Hawaii': 'HI', 'Idaho': 'ID', 'Illinois': 'IL', 'Indiana': 'IN', 'Iowa': 'IA',
            'Kansas': 'KS', 'Kentucky': 'KY', 'Louisiana': 'LA', 'Maine': 'ME', 'Maryland': 'MD',
            'Massachusetts': 'MA', 'Michigan': 'MI', 'Minnesota': 'MN', 'Mississippi': 'MS', 'Missouri': 'MO',
            'Montana': 'MT', 'Nebraska': 'NE', 'Nevada': 'NV', 'New Hampshire': 'NH', 'New Jersey': 'NJ',
            'New Mexico': 'NM', 'New York': 'NY', 'North Carolina': 'NC', 'North Dakota': 'ND', 'Ohio': 'OH',
            'Oklahoma': 'OK', 'Oregon': 'OR', 'Pennsylvania': 'PA', 'Rhode Island': 'RI', 'South Carolina': 'SC',
            'South Dakota': 'SD', 'Tennessee': 'TN', 'Texas': 'TX', 'Utah': 'UT', 'Vermont': 'VT',
            'Virginia': 'VA', 'Washington': 'WA', 'West Virginia': 'WV', 'Wisconsin': 'WI', 'Wyoming': 'WY',
            'District of Columbia': 'DC', 'Puerto Rico': 'PR', 'US Virgin Islands': 'VI', 'Guam': 'GU',
            'American Samoa': 'AS', 'Northern Mariana Islands': 'MP'
        };

        // 🚀 初始化页面
        function initializePage() {
            const stateSelect = document.getElementById('stateSelect');
            
            // 填充州选择器
            Object.keys(US_STATES).sort().forEach(stateName => {
                const option = document.createElement('option');
                option.value = US_STATES[stateName];
                option.textContent = `${stateName} (${US_STATES[stateName]})`;
                stateSelect.appendChild(option);
            });

            // 添加事件监听器
            document.getElementById('countyFilter').addEventListener('input', filterCounties);
            document.getElementById('stateSelect').addEventListener('change', onStateChange);
        }

        // 🔍 搜索地理位置信息
        async function searchLocation() {
            const stateCode = document.getElementById('stateSelect').value;
            const zipCode = document.getElementById('zipCode').value.trim();
            const resultsContent = document.getElementById('resultsContent');

            if (!stateCode && !zipCode) {
                showError('请选择州或输入邮政编码');
                return;
            }

            showLoading();

            try {
                if (zipCode) {
                    await searchByZipCode(zipCode);
                } else {
                    await searchByState(stateCode);
                }
            } catch (error) {
                showError(`查询失败: ${error.message}`);
            }
        }

        // 📮 通过邮政编码查询
        async function searchByZipCode(zipCode) {
            try {
                // 使用Context7获取邮政编码信息
                const zipData = await queryContext7(`US postal code ${zipCode} location details county state`);

                showResults({
                    type: 'zipcode',
                    zipCode: zipCode,
                    data: zipData,
                    message: `邮政编码 ${zipCode} 的详细信息`
                });
            } catch (error) {
                showError(`邮政编码查询失败: ${error.message}`);
            }
        }

        // 🏛️ 通过州查询
        async function searchByState(stateCode) {
            const stateName = Object.keys(US_STATES).find(key => US_STATES[key] === stateCode);

            try {
                // 使用Context7获取真实的县数据
                const countiesData = await queryContext7(`${stateName} state counties list complete all counties`);

                // 解析县数据
                const counties = parseCountiesFromResponse(countiesData, stateName);

                showResults({
                    type: 'state',
                    stateName: stateName,
                    stateCode: stateCode,
                    counties: counties,
                    rawData: countiesData
                });
            } catch (error) {
                console.warn('Context7查询失败，使用模拟数据:', error);
                // 如果Context7查询失败，使用模拟数据
                const mockCounties = generateMockCounties(stateName);
                showResults({
                    type: 'state',
                    stateName: stateName,
                    stateCode: stateCode,
                    counties: mockCounties,
                    isSimulated: true
                });
            }
        }

        // 🔍 Context7查询函数
        async function queryContext7(query) {
            // 注意：这是一个示例实现
            // 实际使用时需要根据Context7的具体API进行调整
            const response = await fetch('/api/context7/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    query: query,
                    maxTokens: 2000
                })
            });

            if (!response.ok) {
                throw new Error(`Context7 API错误: ${response.status}`);
            }

            const data = await response.json();
            return data.result || data.content || data;
        }

        // 📊 解析县数据
        function parseCountiesFromResponse(response, stateName) {
            // 尝试从Context7响应中提取县名列表
            const text = typeof response === 'string' ? response : JSON.stringify(response);

            // 使用正则表达式提取县名
            const countyPattern = /([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+County/g;
            const matches = text.match(countyPattern) || [];

            // 去重并排序
            const counties = [...new Set(matches)].sort();

            // 如果没有找到县，返回模拟数据
            if (counties.length === 0) {
                return generateMockCounties(stateName);
            }

            return counties;
        }

        // 🎭 生成模拟县数据
        function generateMockCounties(stateName) {
            const commonCountyNames = [
                'Washington', 'Jefferson', 'Franklin', 'Jackson', 'Lincoln', 'Madison', 'Monroe', 'Adams',
                'Clay', 'Greene', 'Harrison', 'Johnson', 'Knox', 'Lee', 'Marshall', 'Morgan', 'Perry',
                'Scott', 'Union', 'Warren', 'Wayne', 'Wilson', 'Anderson', 'Brown', 'Clark', 'Davis',
                'Evans', 'Ford', 'Grant', 'Hall', 'King', 'Lewis', 'Martin', 'Nelson', 'Parker', 'Reed',
                'Smith', 'Taylor', 'Turner', 'Walker', 'White', 'Young'
            ];
            
            const stateSpecificCounties = {
                'Texas': ['Harris', 'Dallas', 'Tarrant', 'Bexar', 'Travis', 'Collin', 'Hidalgo', 'Fort Bend'],
                'California': ['Los Angeles', 'San Diego', 'Orange', 'Riverside', 'San Bernardino', 'Santa Clara', 'Alameda'],
                'Florida': ['Miami-Dade', 'Broward', 'Palm Beach', 'Hillsborough', 'Orange', 'Pinellas', 'Duval'],
                'New York': ['Kings', 'Queens', 'New York', 'Suffolk', 'Bronx', 'Nassau', 'Westchester'],
                'Pennsylvania': ['Philadelphia', 'Allegheny', 'Montgomery', 'Bucks', 'Chester', 'Delaware', 'Lancaster']
            };
            
            const counties = stateSpecificCounties[stateName] || commonCountyNames.slice(0, Math.floor(Math.random() * 20) + 10);
            return counties.map(name => `${name} County`);
        }

        // 📊 显示查询结果
        function showResults(data) {
            const resultsContent = document.getElementById('resultsContent');

            if (data.type === 'state') {
                const dataSourceBadge = data.isSimulated ?
                    '<span style="background: #ff9800; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.8em;">模拟数据</span>' :
                    '<span style="background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.8em;">实时数据</span>';

                resultsContent.innerHTML = `
                    <div class="state-info">
                        <h4>🏛️ ${data.stateName} (${data.stateCode}) ${dataSourceBadge}</h4>
                        <div class="stats">
                            <div class="stat-item">
                                <div class="stat-number">${data.counties.length}</div>
                                <div class="stat-label">县数量</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">${data.stateCode}</div>
                                <div class="stat-label">州代码</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">${getStateCapital(data.stateName)}</div>
                                <div class="stat-label">州府</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">${getStateRegion(data.stateName)}</div>
                                <div class="stat-label">地区</div>
                            </div>
                        </div>
                        <div style="margin: 15px 0;">
                            <button onclick="exportCounties('${data.stateName}', '${data.stateCode}')"
                                    style="background: #2196F3; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                                📄 导出县列表
                            </button>
                            <button onclick="searchNearbyStates('${data.stateCode}')"
                                    style="background: #FF9800; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">
                                🗺️ 查看邻近州
                            </button>
                        </div>
                        <div class="counties-grid" id="countiesGrid">
                            ${data.counties.map((county, index) => `
                                <div class="county-item" onclick="selectCounty('${county}', '${data.stateCode}', ${index})"
                                     title="点击查看 ${county} 的详细信息">
                                    📍 ${county}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            } else if (data.type === 'zipcode') {
                resultsContent.innerHTML = `
                    <div class="state-info">
                        <h4>📮 邮政编码 ${data.zipCode} 查询结果</h4>
                        <div class="stats">
                            <div class="stat-item">
                                <div class="stat-number">${data.zipCode}</div>
                                <div class="stat-label">邮政编码</div>
                            </div>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 15px;">
                            <pre style="white-space: pre-wrap; font-family: inherit;">${JSON.stringify(data.data, null, 2)}</pre>
                        </div>
                        <div style="margin: 15px 0;">
                            <button onclick="searchRelatedZips('${data.zipCode}')"
                                    style="background: #4CAF50; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">
                                🔍 查找相关邮政编码
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        // 🏛️ 获取州府信息
        function getStateCapital(stateName) {
            const capitals = {
                'Alabama': 'Montgomery', 'Alaska': 'Juneau', 'Arizona': 'Phoenix', 'Arkansas': 'Little Rock',
                'California': 'Sacramento', 'Colorado': 'Denver', 'Connecticut': 'Hartford', 'Delaware': 'Dover',
                'Florida': 'Tallahassee', 'Georgia': 'Atlanta', 'Hawaii': 'Honolulu', 'Idaho': 'Boise',
                'Illinois': 'Springfield', 'Indiana': 'Indianapolis', 'Iowa': 'Des Moines', 'Kansas': 'Topeka',
                'Kentucky': 'Frankfort', 'Louisiana': 'Baton Rouge', 'Maine': 'Augusta', 'Maryland': 'Annapolis',
                'Massachusetts': 'Boston', 'Michigan': 'Lansing', 'Minnesota': 'Saint Paul', 'Mississippi': 'Jackson',
                'Missouri': 'Jefferson City', 'Montana': 'Helena', 'Nebraska': 'Lincoln', 'Nevada': 'Carson City',
                'New Hampshire': 'Concord', 'New Jersey': 'Trenton', 'New Mexico': 'Santa Fe', 'New York': 'Albany',
                'North Carolina': 'Raleigh', 'North Dakota': 'Bismarck', 'Ohio': 'Columbus', 'Oklahoma': 'Oklahoma City',
                'Oregon': 'Salem', 'Pennsylvania': 'Harrisburg', 'Rhode Island': 'Providence', 'South Carolina': 'Columbia',
                'South Dakota': 'Pierre', 'Tennessee': 'Nashville', 'Texas': 'Austin', 'Utah': 'Salt Lake City',
                'Vermont': 'Montpelier', 'Virginia': 'Richmond', 'Washington': 'Olympia', 'West Virginia': 'Charleston',
                'Wisconsin': 'Madison', 'Wyoming': 'Cheyenne', 'District of Columbia': 'Washington'
            };
            return capitals[stateName] || 'N/A';
        }

        // 🗺️ 获取州地区信息
        function getStateRegion(stateName) {
            const regions = {
                'Northeast': ['Connecticut', 'Maine', 'Massachusetts', 'New Hampshire', 'Rhode Island', 'Vermont', 'New Jersey', 'New York', 'Pennsylvania'],
                'Southeast': ['Delaware', 'Florida', 'Georgia', 'Maryland', 'North Carolina', 'South Carolina', 'Virginia', 'District of Columbia', 'West Virginia', 'Alabama', 'Kentucky', 'Mississippi', 'Tennessee', 'Arkansas', 'Louisiana', 'Oklahoma', 'Texas'],
                'Midwest': ['Illinois', 'Indiana', 'Michigan', 'Ohio', 'Wisconsin', 'Iowa', 'Kansas', 'Minnesota', 'Missouri', 'Nebraska', 'North Dakota', 'South Dakota'],
                'Southwest': ['Arizona', 'New Mexico', 'Nevada', 'Utah'],
                'West': ['Alaska', 'California', 'Colorado', 'Hawaii', 'Idaho', 'Montana', 'Oregon', 'Washington', 'Wyoming']
            };

            for (const [region, states] of Object.entries(regions)) {
                if (states.includes(stateName)) {
                    return region;
                }
            }
            return 'Other';
        }

        // 🔍 筛选县
        function filterCounties() {
            const filter = document.getElementById('countyFilter').value.toLowerCase();
            const countyItems = document.querySelectorAll('.county-item');
            
            countyItems.forEach(item => {
                const countyName = item.textContent.toLowerCase();
                if (countyName.includes(filter)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // 🏛️ 州选择变化
        function onStateChange() {
            document.getElementById('countyFilter').value = '';
        }

        // 📍 选择县
        async function selectCounty(countyName, stateCode, index) {
            const modal = createCountyModal(countyName, stateCode);
            document.body.appendChild(modal);

            // 尝试获取县的详细信息
            try {
                const countyData = await queryContext7(`${countyName} ${stateCode} county population demographics zip codes`);
                updateCountyModal(modal, countyData);
            } catch (error) {
                console.warn('无法获取县详细信息:', error);
            }
        }

        // 🏛️ 创建县信息模态框
        function createCountyModal(countyName, stateCode) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 30px; max-width: 600px; width: 90%; max-height: 80%; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3>📍 ${countyName}, ${stateCode}</h3>
                        <button onclick="this.closest('div').parentElement.remove()"
                                style="background: #f44336; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer;">×</button>
                    </div>
                    <div id="countyDetails">
                        <div style="text-align: center; padding: 20px; color: #666;">
                            🔄 正在加载县详细信息...
                        </div>
                    </div>
                </div>
            `;

            return modal;
        }

        // 📊 更新县信息模态框
        function updateCountyModal(modal, data) {
            const detailsDiv = modal.querySelector('#countyDetails');
            detailsDiv.innerHTML = `
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                    <h4>📊 县信息</h4>
                    <pre style="white-space: pre-wrap; font-family: inherit; font-size: 0.9em;">${JSON.stringify(data, null, 2)}</pre>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                    <button onclick="copyToClipboard('${JSON.stringify(data).replace(/'/g, "\\'")}'); alert('数据已复制到剪贴板！');"
                            style="background: #4CAF50; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">
                        📋 复制数据
                    </button>
                    <button onclick="downloadCountyData('${modal.querySelector('h3').textContent}', '${JSON.stringify(data).replace(/'/g, "\\'")}');"
                            style="background: #2196F3; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">
                        💾 下载数据
                    </button>
                </div>
            `;
        }

        // 📄 导出县列表
        function exportCounties(stateName, stateCode) {
            const counties = Array.from(document.querySelectorAll('.county-item')).map(item =>
                item.textContent.replace('📍 ', '').trim()
            );

            const csvContent = `State,State Code,County\n${counties.map(county =>
                `"${stateName}","${stateCode}","${county}"`
            ).join('\n')}`;

            downloadFile(`${stateName}_Counties.csv`, csvContent, 'text/csv');
        }

        // 🗺️ 搜索邻近州
        async function searchNearbyStates(stateCode) {
            try {
                const nearbyData = await queryContext7(`states bordering ${stateCode} neighboring states`);
                alert(`邻近州信息:\n\n${JSON.stringify(nearbyData, null, 2)}`);
            } catch (error) {
                alert('无法获取邻近州信息');
            }
        }

        // 🔍 搜索相关邮政编码
        async function searchRelatedZips(zipCode) {
            try {
                const relatedData = await queryContext7(`zip codes near ${zipCode} surrounding postal codes`);
                alert(`相关邮政编码:\n\n${JSON.stringify(relatedData, null, 2)}`);
            } catch (error) {
                alert('无法获取相关邮政编码信息');
            }
        }

        // 📋 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).catch(err => {
                console.error('复制失败:', err);
            });
        }

        // 💾 下载文件
        function downloadFile(filename, content, mimeType = 'text/plain') {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 💾 下载县数据
        function downloadCountyData(countyName, data) {
            const filename = `${countyName.replace(/[^a-zA-Z0-9]/g, '_')}_Data.json`;
            downloadFile(filename, data, 'application/json');
        }

        // ⏳ 显示加载状态
        function showLoading() {
            document.getElementById('resultsContent').innerHTML = `
                <div class="loading">
                    🔄 正在查询地理信息，请稍候...
                </div>
            `;
        }

        // ❌ 显示错误信息
        function showError(message) {
            document.getElementById('resultsContent').innerHTML = `
                <div class="error">
                    ❌ ${message}
                </div>
            `;
        }

        // 🚀 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
