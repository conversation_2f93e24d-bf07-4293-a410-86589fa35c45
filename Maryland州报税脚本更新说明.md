# Maryland州报税脚本更新说明

## 🎯 更新目标
根据用户要求，为脚本添加处理Maryland州报税页面的功能，并且所有健康保险相关问题都选择"No"。

## 📋 新增功能

### 1. 新增函数：`step28HandleMarylandReturn()`
- **位置**：第1296行
- **功能**：专门处理Maryland州报税页面
- **检测条件**：
  - URL包含 `/StateReturn/MD`
  - 页面标题包含 "Maryland Return"

### 2. 页面处理逻辑

#### A. 县选择
- 自动选择 "BALTIMORE COUNTY"
- 适用于Lutherville Timonium地区

#### B. 地址信息填写
- 物理街道地址：16 EVANS
- 邮编：21093
- 城市：LUTHERVILLE TIMONIUM
- 州：Maryland
- 电子邮件：<EMAIL>

#### C. 健康保险问题处理（全部选择No）
1. **问题1**：Do you have health care coverage? → **No**
2. **问题2**：Do you authorize the Comptroller of Maryland to share information...? → **No**
3. **问题3**：If claiming dependents, do all dependents have Health Care Coverage? → **No**

### 3. 脚本集成

#### A. 添加到主检测循环
- **位置**：第3378-3386行
- **检测条件**：URL包含 `/StateReturn/MD` 且未执行过
- **执行标志**：`executedSteps.marylandReturn`

#### B. 更新执行状态管理
- **executedSteps对象**：添加 `marylandReturn: false`
- **resetSteps函数**：添加重置逻辑

## 🔧 技术实现细节

### 1. 智能元素查找
使用 `findElementBySelectors()` 函数，支持多种选择器：
```javascript
const countySelectors = [
    'select[name*="county"]',
    'combobox[name*="county"]',
    'select:contains("Select")',
    'combobox:contains("Select")'
];
```

### 2. 健康保险问题处理
通过遍历所有radio按钮，根据父元素文本内容智能匹配：
```javascript
const allRadios = document.querySelectorAll('input[type="radio"]');
for (const radio of allRadios) {
    const parentText = radio.closest('div, fieldset, section')?.textContent || '';
    if (parentText.includes('health care coverage') && 
        (radio.value === 'No' || radio.nextElementSibling?.textContent?.includes('No'))) {
        forceClick(radio);
        break;
    }
}
```

### 3. 执行时序控制
使用setTimeout确保页面元素完全加载：
- 初始等待：2秒
- 地址填写：1秒后
- 健康问题：2秒后
- 提交按钮：4秒后

## 📝 使用说明

### 1. 脚本激活
当页面URL包含 `/StateReturn/MD` 时，脚本会自动检测并执行Maryland州报税处理。

### 2. 执行流程
1. 检测Maryland州报税页面
2. 选择Baltimore County
3. 填写/验证地址信息
4. 处理健康保险问题（全选No）
5. 点击Continue按钮提交

### 3. 日志输出
脚本会在控制台输出详细的执行日志：
- 页面检测确认
- 元素查找状态
- 字段填写进度
- 按钮点击结果

## ⚠️ 注意事项

### 1. 页面兼容性
- 脚本使用多种选择器策略，提高兼容性
- 支持不同的页面布局和元素结构

### 2. 错误处理
- 每个元素查找都有try-catch保护
- 未找到元素时会记录日志但不中断执行

### 3. 执行控制
- 使用 `executedSteps.marylandReturn` 防止重复执行
- 30秒自动重置执行状态

## 🎉 预期效果

用户在Maryland州报税页面时，脚本将：
1. ✅ 自动选择正确的县（Baltimore County）
2. ✅ 确认地址信息正确填写
3. ✅ 所有健康保险问题选择"No"
4. ✅ 自动提交并进入下一步

这样用户就不需要手动处理Maryland州报税页面的复杂表单，脚本会按照指定的选择（全部选No）自动完成。
