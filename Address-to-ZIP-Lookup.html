<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📮 地址邮政编码查询工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .search-form {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row.full {
            grid-template-columns: 1fr;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
            font-size: 1.1em;
        }

        .form-group input,
        .form-group select {
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }

        .search-btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            width: 100%;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
        }

        .search-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .results-section {
            background: white;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            overflow: hidden;
        }

        .results-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .results-header h3 {
            color: #333;
            font-size: 1.5em;
        }

        .results-content {
            padding: 20px;
        }

        .result-item {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 5px solid #4CAF50;
        }

        .zip-code {
            font-size: 2em;
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 10px;
        }

        .address-info {
            color: #666;
            margin-bottom: 15px;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .btn-copy {
            background: #2196F3;
            color: white;
        }

        .btn-copy:hover {
            background: #1976D2;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 1.2em;
        }

        .error {
            background: #ffebee;
            color: #c62828;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #f44336;
            margin: 20px 0;
        }

        .example-addresses {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 5px solid #2196F3;
        }

        .example-addresses h4 {
            color: #1976D2;
            margin-bottom: 10px;
        }

        .example-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .example-item:hover {
            background: #f0f8f0;
            transform: translateX(5px);
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📮 地址邮政编码查询</h1>
            <p>输入地址、城市、州，快速查找对应的ZIP邮政编码</p>
        </div>

        <div class="main-content">
            <div class="search-form">
                <div class="form-row full">
                    <div class="form-group">
                        <label for="streetAddress">🏠 街道地址</label>
                        <input type="text" id="streetAddress" placeholder="例如: 1600 Pennsylvania Avenue NW">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="city">🏙️ 城市</label>
                        <input type="text" id="city" placeholder="例如: Washington">
                    </div>
                    
                    <div class="form-group">
                        <label for="state">🏛️ 州</label>
                        <select id="state">
                            <option value="">-- 选择州 --</option>
                        </select>
                    </div>
                </div>
                
                <button class="search-btn" onclick="searchZipCode()">
                    🔍 查找邮政编码
                </button>
            </div>

            <div class="results-section">
                <div class="results-header">
                    <h3>📊 查询结果</h3>
                </div>
                <div class="results-content" id="resultsContent">
                    <div class="loading">
                        👋 欢迎使用地址邮政编码查询工具！<br>
                        请输入完整地址信息开始查询
                    </div>
                </div>
            </div>

            <div class="example-addresses">
                <h4>💡 示例地址</h4>
                <div class="example-item" onclick="fillExample('1600 Pennsylvania Avenue NW', 'Washington', 'DC')">
                    📍 1600 Pennsylvania Avenue NW, Washington, DC (白宫)
                </div>
                <div class="example-item" onclick="fillExample('350 Fifth Avenue', 'New York', 'NY')">
                    📍 350 Fifth Avenue, New York, NY (帝国大厦)
                </div>
                <div class="example-item" onclick="fillExample('1 Infinite Loop', 'Cupertino', 'CA')">
                    📍 1 Infinite Loop, Cupertino, CA (苹果公司)
                </div>
                <div class="example-item" onclick="fillExample('221B Baker Street', 'London', 'TX')">
                    📍 221B Baker Street, London, TX (德克萨斯州伦敦市)
                </div>
                <div class="example-item" onclick="fillExample('3120 M St NW', 'Washington', 'DC')">
                    📍 3120 M St NW, Washington, DC (USPS API测试地址)
                </div>
                <div class="example-item" onclick="fillExample('1273 Pale San Vitores RD', 'Tamuning', 'GU')">
                    📍 1273 Pale San Vitores RD, Tamuning, GU (关岛地址)
                </div>
            </div>

            <!-- USPS API配置区域 -->
            <div class="example-addresses" style="border-left-color: #2196F3;">
                <h4>🔧 USPS API配置</h4>
                <div style="display: grid; grid-template-columns: 1fr auto; gap: 10px; align-items: center; margin-bottom: 10px;">
                    <input type="password" id="uspsToken" placeholder="输入USPS API访问令牌..."
                           style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 5px;">
                    <button onclick="saveUSPSToken()"
                            style="background: #4CAF50; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">
                        💾 保存令牌
                    </button>
                </div>
                <div style="font-size: 0.9em; color: #666; margin-bottom: 10px;">
                    <strong>📋 如何获取USPS API令牌:</strong><br>
                    1. 访问 <a href="https://developer.usps.com" target="_blank">USPS开发者门户</a><br>
                    2. 注册账户并创建应用<br>
                    3. 获取Client ID和Client Secret<br>
                    4. 使用OAuth 2.0获取访问令牌
                </div>
                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <button onclick="testUSPSConnection()"
                            style="background: #2196F3; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">
                        🔍 测试连接
                    </button>
                    <button onclick="clearUSPSToken()"
                            style="background: #f44336; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">
                        🗑️ 清除令牌
                    </button>
                    <button onclick="showUSPSHelp()"
                            style="background: #FF9800; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">
                        ❓ 帮助
                    </button>
                </div>
                <div id="uspsStatus" style="margin-top: 10px; padding: 8px; border-radius: 5px; display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        // 🗺️ 美国所有州和领土
        const US_STATES = {
            'Alabama': 'AL', 'Alaska': 'AK', 'Arizona': 'AZ', 'Arkansas': 'AR', 'California': 'CA',
            'Colorado': 'CO', 'Connecticut': 'CT', 'Delaware': 'DE', 'Florida': 'FL', 'Georgia': 'GA',
            'Hawaii': 'HI', 'Idaho': 'ID', 'Illinois': 'IL', 'Indiana': 'IN', 'Iowa': 'IA',
            'Kansas': 'KS', 'Kentucky': 'KY', 'Louisiana': 'LA', 'Maine': 'ME', 'Maryland': 'MD',
            'Massachusetts': 'MA', 'Michigan': 'MI', 'Minnesota': 'MN', 'Mississippi': 'MS', 'Missouri': 'MO',
            'Montana': 'MT', 'Nebraska': 'NE', 'Nevada': 'NV', 'New Hampshire': 'NH', 'New Jersey': 'NJ',
            'New Mexico': 'NM', 'New York': 'NY', 'North Carolina': 'NC', 'North Dakota': 'ND', 'Ohio': 'OH',
            'Oklahoma': 'OK', 'Oregon': 'OR', 'Pennsylvania': 'PA', 'Rhode Island': 'RI', 'South Carolina': 'SC',
            'South Dakota': 'SD', 'Tennessee': 'TN', 'Texas': 'TX', 'Utah': 'UT', 'Vermont': 'VT',
            'Virginia': 'VA', 'Washington': 'WA', 'West Virginia': 'WV', 'Wisconsin': 'WI', 'Wyoming': 'WY',
            'District of Columbia': 'DC', 'Puerto Rico': 'PR'
        };

        // 🚀 初始化页面
        function initializePage() {
            const stateSelect = document.getElementById('state');
            
            // 填充州选择器
            Object.keys(US_STATES).sort().forEach(stateName => {
                const option = document.createElement('option');
                option.value = US_STATES[stateName];
                option.textContent = `${stateName} (${US_STATES[stateName]})`;
                stateSelect.appendChild(option);
            });
        }

        // 🔍 搜索邮政编码
        async function searchZipCode() {
            const streetAddress = document.getElementById('streetAddress').value.trim();
            const city = document.getElementById('city').value.trim();
            const state = document.getElementById('state').value;

            if (!streetAddress || !city || !state) {
                showError('请填写完整的地址信息（街道地址、城市、州）');
                return;
            }

            showLoading();

            try {
                // 构建查询字符串
                const fullAddress = `${streetAddress}, ${city}, ${state}`;
                
                // 使用Context7查询邮政编码
                const zipData = await queryZipCodeWithContext7(fullAddress);
                
                showResults(zipData, fullAddress);
            } catch (error) {
                showError(`查询失败: ${error.message}`);
            }
        }

        // 🔍 使用USPS API查询邮政编码
        async function queryZipCodeWithContext7(address) {
            const addressParts = address.split(',');
            const streetAddress = addressParts[0].trim();
            const city = addressParts[1].trim();
            const state = addressParts[2].trim();

            try {
                // 首先尝试使用真实的USPS API
                const uspsResult = await queryUSPSAPI(streetAddress, city, state);
                if (uspsResult) {
                    return uspsResult;
                }
            } catch (error) {
                console.warn('USPS API查询失败:', error);
            }

            try {
                // 如果USPS API不可用，尝试Context7
                const response = await fetch('/api/context7/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: `Find the ZIP postal code for this address: ${address}. Return the 5-digit ZIP code and any additional location information.`,
                        maxTokens: 1000
                    })
                });

                if (!response.ok) {
                    throw new Error(`Context7 API错误: ${response.status}`);
                }

                const data = await response.json();
                return parseZipCodeResponse(data, address);
            } catch (error) {
                // 如果都不可用，使用模拟数据
                console.warn('所有API查询失败，使用模拟数据:', error);
                return generateMockZipData(address);
            }
        }

        // 🏛️ 使用USPS官方API查询邮政编码
        async function queryUSPSAPI(streetAddress, city, state) {
            // 注意：这需要有效的USPS API令牌
            // 实际使用时需要先获取OAuth令牌

            const USPS_API_BASE = 'https://apis.usps.com';
            const token = localStorage.getItem('usps_access_token'); // 需要先获取令牌

            if (!token) {
                throw new Error('USPS API令牌未找到');
            }

            try {
                // 使用USPS Address API的zipcode端点
                const encodedStreet = encodeURIComponent(streetAddress);
                const encodedCity = encodeURIComponent(city);
                const encodedState = encodeURIComponent(state);

                const url = `${USPS_API_BASE}/addresses/v3/zipcode?streetAddress=${encodedStreet}&city=${encodedCity}&state=${encodedState}`;

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`USPS API错误: ${response.status}`);
                }

                const data = await response.json();

                return {
                    zipCode: data.address.ZIPCode,
                    zipPlus4: data.address.ZIPPlus4,
                    address: `${data.address.streetAddress}, ${data.address.city}, ${data.address.state}`,
                    source: 'USPS官方API',
                    additionalInfo: `标准化地址: ${data.address.streetAddress}, ${data.address.city}, ${data.address.state} ${data.address.ZIPCode}-${data.address.ZIPPlus4}`,
                    rawData: data
                };
            } catch (error) {
                throw new Error(`USPS API查询失败: ${error.message}`);
            }
        }

        // 📊 解析邮政编码响应
        function parseZipCodeResponse(response, address) {
            const text = typeof response === 'string' ? response : JSON.stringify(response);
            
            // 使用正则表达式提取5位数邮政编码
            const zipPattern = /\b\d{5}\b/g;
            const matches = text.match(zipPattern) || [];
            
            if (matches.length > 0) {
                return {
                    zipCode: matches[0],
                    address: address,
                    source: 'Context7',
                    additionalInfo: text
                };
            } else {
                return generateMockZipData(address);
            }
        }

        // 🎭 生成模拟邮政编码数据
        function generateMockZipData(address) {
            // 基于州生成模拟邮政编码
            const stateZipRanges = {
                'CA': ['90000', '96199'], 'NY': ['10000', '14999'], 'TX': ['73000', '79999'],
                'FL': ['32000', '34999'], 'IL': ['60000', '62999'], 'PA': ['15000', '19699'],
                'OH': ['43000', '45999'], 'GA': ['30000', '31999'], 'NC': ['27000', '28999'],
                'MI': ['48000', '49999'], 'NJ': ['07000', '08999'], 'VA': ['20000', '24699'],
                'WA': ['98000', '99499'], 'AZ': ['85000', '86599'], 'MA': ['01000', '02799'],
                'TN': ['37000', '38599'], 'IN': ['46000', '47999'], 'MO': ['63000', '65899'],
                'MD': ['20600', '21999'], 'WI': ['53000', '54999'], 'CO': ['80000', '81699'],
                'MN': ['55000', '56799'], 'SC': ['29000', '29999'], 'AL': ['35000', '36999'],
                'LA': ['70000', '71499'], 'KY': ['40000', '42799'], 'OR': ['97000', '97999'],
                'OK': ['73000', '74999'], 'CT': ['06000', '06999'], 'IA': ['50000', '52899'],
                'MS': ['38600', '39799'], 'AR': ['71600', '72999'], 'UT': ['84000', '84799'],
                'KS': ['66000', '67999'], 'NV': ['89000', '89999'], 'NM': ['87000', '88499'],
                'NE': ['68000', '69399'], 'WV': ['24700', '26899'], 'ID': ['83200', '83899'],
                'HI': ['96700', '96899'], 'NH': ['03000', '03899'], 'ME': ['03900', '04999'],
                'RI': ['02800', '02999'], 'MT': ['59000', '59999'], 'DE': ['19700', '19999'],
                'SD': ['57000', '57799'], 'ND': ['58000', '58899'], 'AK': ['99500', '99999'],
                'DC': ['20000', '20599'], 'VT': ['05000', '05999'], 'WY': ['82000', '83199']
            };
            
            const addressParts = address.split(',');
            const state = addressParts[addressParts.length - 1].trim();
            const range = stateZipRanges[state] || ['10000', '99999'];
            
            const minZip = parseInt(range[0]);
            const maxZip = parseInt(range[1]);
            const mockZip = Math.floor(Math.random() * (maxZip - minZip + 1)) + minZip;
            
            return {
                zipCode: mockZip.toString(),
                address: address,
                source: '模拟数据',
                additionalInfo: `基于${state}州的邮政编码范围生成的模拟数据`
            };
        }

        // 📊 显示查询结果
        function showResults(data, address) {
            const resultsContent = document.getElementById('resultsContent');

            // 根据数据源设置颜色和图标
            let sourceColor, sourceIcon;
            switch (data.source) {
                case 'USPS官方API':
                    sourceColor = '#4CAF50';
                    sourceIcon = '🏛️';
                    break;
                case 'Context7':
                    sourceColor = '#2196F3';
                    sourceIcon = '🤖';
                    break;
                default:
                    sourceColor = '#ff9800';
                    sourceIcon = '🎭';
            }

            // 构建ZIP+4显示
            const fullZip = data.zipPlus4 ? `${data.zipCode}-${data.zipPlus4}` : data.zipCode;

            resultsContent.innerHTML = `
                <div class="result-item">
                    <div class="zip-code">📮 ${fullZip}</div>
                    <div class="address-info">
                        <strong>📍 查询地址:</strong> ${address}<br>
                        ${data.address !== address ? `<strong>📍 标准化地址:</strong> ${data.address}<br>` : ''}
                        <strong>📊 数据源:</strong> <span style="color: ${sourceColor}; font-weight: bold;">${sourceIcon} ${data.source}</span>
                        ${data.zipPlus4 ? `<br><strong>📮 完整邮政编码:</strong> ${data.zipCode}-${data.zipPlus4}` : ''}
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-copy" onclick="copyToClipboard('${data.zipCode}')">
                            📋 复制ZIP
                        </button>
                        ${data.zipPlus4 ? `
                            <button class="btn btn-copy" onclick="copyToClipboard('${data.zipCode}-${data.zipPlus4}')">
                                📋 复制ZIP+4
                            </button>
                        ` : ''}
                        <button class="btn btn-copy" onclick="copyToClipboard('${data.address}')">
                            📋 复制地址
                        </button>
                        <button class="btn btn-copy" onclick="searchNearbyZips('${data.zipCode}')">
                            🔍 查询城市州信息
                        </button>
                        ${data.rawData ? `
                            <button class="btn btn-copy" onclick="showRawData('${JSON.stringify(data.rawData).replace(/'/g, "\\'")}')">
                                📄 查看原始数据
                            </button>
                        ` : ''}
                    </div>
                    ${data.additionalInfo ? `
                        <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px; font-size: 0.9em;">
                            <strong>ℹ️ 附加信息:</strong><br>
                            ${data.additionalInfo}
                        </div>
                    ` : ''}
                    ${data.source === 'USPS官方API' ? `
                        <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-radius: 5px; font-size: 0.9em; border-left: 4px solid #4CAF50;">
                            <strong>✅ USPS官方验证:</strong> 此地址已通过美国邮政服务官方API验证，确保准确性。
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // 📄 显示原始数据
        function showRawData(rawDataStr) {
            const rawData = JSON.parse(rawDataStr);
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center; padding: 20px;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 30px; max-width: 800px; width: 100%; max-height: 80%; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3>📄 USPS API原始数据</h3>
                        <button onclick="this.closest('div').parentElement.remove()"
                                style="background: #f44336; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer;">×</button>
                    </div>
                    <pre style="background: #f8f9fa; padding: 15px; border-radius: 8px; overflow-x: auto; font-size: 0.9em; white-space: pre-wrap;">${JSON.stringify(rawData, null, 2)}</pre>
                    <div style="margin-top: 15px; display: flex; gap: 10px;">
                        <button onclick="copyToClipboard('${rawDataStr.replace(/'/g, "\\'")}'); alert('原始数据已复制！');"
                                style="background: #4CAF50; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                            📋 复制JSON数据
                        </button>
                        <button onclick="downloadJSON('usps-address-data.json', '${rawDataStr.replace(/'/g, "\\'")}');"
                                style="background: #2196F3; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                            💾 下载JSON文件
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // 💾 下载JSON文件
        function downloadJSON(filename, jsonStr) {
            const blob = new Blob([jsonStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 💡 填充示例地址
        function fillExample(street, city, state) {
            document.getElementById('streetAddress').value = street;
            document.getElementById('city').value = city;
            document.getElementById('state').value = state;
        }

        // 🔍 查找附近邮政编码
        async function searchNearbyZips(zipCode) {
            try {
                const nearbyData = await queryZipCodeWithContext7(`ZIP codes near ${zipCode}`);
                alert(`附近邮政编码信息:\n\n${nearbyData.additionalInfo || '查询完成'}`);
            } catch (error) {
                alert('无法获取附近邮政编码信息');
            }
        }

        // 📋 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                // 显示复制成功提示
                const toast = document.createElement('div');
                toast.textContent = `✅ 已复制: ${text}`;
                toast.style.cssText = `
                    position: fixed; top: 20px; right: 20px; background: #4CAF50; 
                    color: white; padding: 10px 20px; border-radius: 5px; z-index: 1000;
                    animation: fadeInOut 3s ease-in-out;
                `;
                document.body.appendChild(toast);
                setTimeout(() => document.body.removeChild(toast), 3000);
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            });
        }

        // ⏳ 显示加载状态
        function showLoading() {
            document.getElementById('resultsContent').innerHTML = `
                <div class="loading">
                    🔄 正在查询邮政编码，请稍候...
                </div>
            `;
        }

        // ❌ 显示错误信息
        function showError(message) {
            document.getElementById('resultsContent').innerHTML = `
                <div class="error">
                    ❌ ${message}
                </div>
            `;
        }

        // 💾 保存USPS API令牌
        function saveUSPSToken() {
            const token = document.getElementById('uspsToken').value.trim();
            if (!token) {
                showUSPSStatus('请输入有效的USPS API令牌', 'error');
                return;
            }

            localStorage.setItem('usps_access_token', token);
            document.getElementById('uspsToken').value = '';
            showUSPSStatus('✅ USPS API令牌已保存', 'success');
        }

        // 🗑️ 清除USPS API令牌
        function clearUSPSToken() {
            localStorage.removeItem('usps_access_token');
            showUSPSStatus('🗑️ USPS API令牌已清除', 'info');
        }

        // 🔍 测试USPS API连接
        async function testUSPSConnection() {
            const token = localStorage.getItem('usps_access_token');
            if (!token) {
                showUSPSStatus('❌ 请先保存USPS API令牌', 'error');
                return;
            }

            showUSPSStatus('🔄 正在测试USPS API连接...', 'info');

            try {
                // 使用USPS API测试地址验证连接
                const testResult = await queryUSPSAPI('3120 M St NW', 'Washington', 'DC');
                if (testResult) {
                    showUSPSStatus(`✅ USPS API连接成功！测试结果: ${testResult.zipCode}`, 'success');
                } else {
                    showUSPSStatus('❌ USPS API连接失败', 'error');
                }
            } catch (error) {
                showUSPSStatus(`❌ USPS API连接失败: ${error.message}`, 'error');
            }
        }

        // 📊 显示USPS状态
        function showUSPSStatus(message, type) {
            const statusDiv = document.getElementById('uspsStatus');
            statusDiv.style.display = 'block';
            statusDiv.textContent = message;

            // 设置样式
            switch (type) {
                case 'success':
                    statusDiv.style.background = '#e8f5e8';
                    statusDiv.style.color = '#2e7d32';
                    statusDiv.style.border = '1px solid #4CAF50';
                    break;
                case 'error':
                    statusDiv.style.background = '#ffebee';
                    statusDiv.style.color = '#c62828';
                    statusDiv.style.border = '1px solid #f44336';
                    break;
                case 'info':
                    statusDiv.style.background = '#e3f2fd';
                    statusDiv.style.color = '#1976D2';
                    statusDiv.style.border = '1px solid #2196F3';
                    break;
            }

            // 3秒后自动隐藏
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }

        // ❓ 显示USPS API帮助
        function showUSPSHelp() {
            const helpText = `
🔧 USPS API配置帮助

📋 获取USPS API令牌的步骤：

1️⃣ 注册USPS开发者账户
   • 访问: https://developer.usps.com
   • 点击"Register"创建账户
   • 验证邮箱地址

2️⃣ 创建应用程序
   • 登录开发者门户
   • 点击"Create App"
   • 填写应用信息
   • 获取Client ID和Client Secret

3️⃣ 获取访问令牌
   • 使用OAuth 2.0授权流程
   • 或使用Client Credentials Grant
   • 示例cURL命令：

   curl -X POST 'https://apis.usps.com/oauth2/v3/token' \\
        -H 'Content-Type: application/json' \\
        -d '{
          "client_id": "YOUR_CLIENT_ID",
          "client_secret": "YOUR_CLIENT_SECRET",
          "grant_type": "client_credentials",
          "scope": "addresses"
        }'

4️⃣ 使用令牌
   • 复制返回的access_token
   • 粘贴到上面的令牌输入框
   • 点击"保存令牌"

⚠️ 注意事项：
• 令牌有有效期限制
• 需要定期刷新令牌
• 保护好您的Client Secret
• 遵守USPS API使用条款

🔗 相关链接：
• USPS开发者文档: https://developer.usps.com/api
• API参考: https://developer.usps.com/api/addresses
• OAuth指南: https://developer.usps.com/api/oauth
            `;

            alert(helpText);
        }

        // 🔍 增强的附近邮政编码查询
        async function searchNearbyZips(zipCode) {
            try {
                const token = localStorage.getItem('usps_access_token');
                if (token) {
                    // 使用USPS API查询城市和州
                    const response = await fetch(`https://apis.usps.com/addresses/v3/city-state?ZIPCode=${zipCode}`, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        alert(`📮 邮政编码 ${zipCode} 信息:\n\n🏙️ 城市: ${data.city}\n🏛️ 州: ${data.state}\n\n💡 提示: 这是USPS官方API提供的准确信息！`);
                        return;
                    }
                }

                // 如果USPS API不可用，使用Context7
                const nearbyData = await queryZipCodeWithContext7(`ZIP codes near ${zipCode}`);
                alert(`附近邮政编码信息:\n\n${nearbyData.additionalInfo || '查询完成'}`);
            } catch (error) {
                alert('无法获取附近邮政编码信息');
            }
        }

        // 🚀 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initializePage();

            // 检查是否已保存USPS令牌
            const savedToken = localStorage.getItem('usps_access_token');
            if (savedToken) {
                showUSPSStatus('✅ 已检测到保存的USPS API令牌', 'success');
            }
        });
    </script>

    <style>
        @keyframes fadeInOut {
            0% { opacity: 0; transform: translateY(-20px); }
            20% { opacity: 1; transform: translateY(0); }
            80% { opacity: 1; transform: translateY(0); }
            100% { opacity: 0; transform: translateY(-20px); }
        }
    </style>
</body>
</html>
