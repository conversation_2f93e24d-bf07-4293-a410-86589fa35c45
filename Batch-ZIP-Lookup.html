<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📦 批量地址邮政编码查询工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .input-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
        }

        .input-methods {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .input-method {
            background: white;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #ddd;
        }

        .input-method h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .textarea-input {
            width: 100%;
            height: 200px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9em;
            resize: vertical;
        }

        .textarea-input:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .file-upload {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-upload:hover {
            border-color: #4CAF50;
            background: #f8f9fa;
        }

        .file-upload.dragover {
            border-color: #4CAF50;
            background: #e8f5e8;
        }

        .file-input {
            display: none;
        }

        .settings-section {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 5px solid #2196F3;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: center;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }

        .form-group select,
        .form-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9em;
        }

        .control-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(76, 175, 80, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-section {
            background: white;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            margin-bottom: 30px;
            overflow: hidden;
        }

        .progress-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .progress-content {
            padding: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .progress-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            text-align: center;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .stat-number {
            font-size: 1.8em;
            font-weight: bold;
            color: #4CAF50;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .results-section {
            background: white;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            overflow: hidden;
        }

        .results-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 2px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9em;
        }

        .results-table th,
        .results-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .results-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            position: sticky;
            top: 0;
        }

        .results-table tbody {
            max-height: 400px;
            overflow-y: auto;
        }

        .status-success {
            color: #28a745;
            font-weight: bold;
        }

        .status-error {
            color: #dc3545;
            font-weight: bold;
        }

        .status-pending {
            color: #ffc107;
            font-weight: bold;
        }

        .log-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.8em;
            border: 1px solid #e9ecef;
        }

        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #f44336;
            margin: 15px 0;
        }

        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
            margin: 15px 0;
        }

        @media (max-width: 768px) {
            .input-methods {
                grid-template-columns: 1fr;
            }
            
            .settings-grid {
                grid-template-columns: 1fr;
            }
            
            .control-buttons {
                flex-direction: column;
            }
            
            .results-header {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📦 批量地址邮政编码查询</h1>
            <p>支持批量导入地址列表，使用免费API快速查询邮政编码</p>
        </div>

        <div class="main-content">
            <!-- 输入区域 -->
            <div class="input-section">
                <h3 style="margin-bottom: 20px; color: #333;">📝 输入地址数据</h3>
                
                <div class="input-methods">
                    <!-- 文本输入 -->
                    <div class="input-method">
                        <h3>✏️ 手动输入</h3>
                        <textarea 
                            id="addressTextarea" 
                            class="textarea-input" 
                            placeholder="每行一个地址，格式：
街道地址, 城市, 州
例如：
1600 Pennsylvania Avenue NW, Washington, DC
350 Fifth Avenue, New York, NY
1 Infinite Loop, Cupertino, CA"></textarea>
                        <button class="btn btn-secondary" onclick="parseTextInput()" style="margin-top: 10px; width: 100%;">
                            📝 解析文本地址
                        </button>
                    </div>

                    <!-- 文件上传 -->
                    <div class="input-method">
                        <h3>📁 文件上传</h3>
                        <div class="file-upload" onclick="document.getElementById('fileInput').click()">
                            <div style="font-size: 2em; margin-bottom: 10px;">📄</div>
                            <div style="font-weight: bold; margin-bottom: 5px;">点击选择文件或拖拽到此处</div>
                            <div style="color: #666; font-size: 0.9em;">支持 CSV, TXT, Excel 文件</div>
                            <input type="file" id="fileInput" class="file-input" accept=".csv,.txt,.xlsx,.xls" onchange="handleFileUpload(event)">
                        </div>
                        <div style="margin-top: 15px; font-size: 0.9em; color: #666;">
                            <strong>CSV格式示例:</strong><br>
                            Street,City,State<br>
                            1600 Pennsylvania Avenue NW,Washington,DC<br>
                            350 Fifth Avenue,New York,NY
                        </div>
                    </div>
                </div>

                <!-- 设置区域 -->
                <div class="settings-section">
                    <h4 style="margin-bottom: 15px; color: #1976D2;">⚙️ 批量查询设置</h4>
                    <div class="settings-grid">
                        <div class="form-group">
                            <label>🌐 API选择</label>
                            <select id="apiSelect">
                                <option value="nominatim">Nominatim (免费无限)</option>
                                <option value="geocoding">Geocoding API</option>
                                <option value="mapbox">MapBox API</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>⏱️ 查询间隔 (毫秒)</label>
                            <input type="number" id="delayInput" value="1000" min="100" max="10000">
                        </div>
                        <div class="form-group">
                            <label>🔄 重试次数</label>
                            <input type="number" id="retryInput" value="3" min="1" max="10">
                        </div>
                        <div class="form-group">
                            <label>📊 并发数量</label>
                            <input type="number" id="concurrentInput" value="1" min="1" max="5">
                        </div>
                    </div>
                </div>

                <!-- 控制按钮 -->
                <div class="control-buttons">
                    <button class="btn btn-primary" onclick="startBatchQuery()" id="startBtn">
                        🚀 开始批量查询
                    </button>
                    <button class="btn btn-secondary" onclick="pauseQuery()" id="pauseBtn" disabled>
                        ⏸️ 暂停查询
                    </button>
                    <button class="btn btn-danger" onclick="stopQuery()" id="stopBtn" disabled>
                        ⏹️ 停止查询
                    </button>
                    <button class="btn btn-secondary" onclick="clearResults()">
                        🗑️ 清空结果
                    </button>
                    <button class="btn btn-secondary" onclick="exportResults()">
                        💾 导出结果
                    </button>
                </div>
            </div>

            <!-- 进度区域 -->
            <div class="progress-section" id="progressSection" style="display: none;">
                <div class="progress-header">
                    <h3>📊 查询进度</h3>
                </div>
                <div class="progress-content">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-stats">
                        <div class="stat-item">
                            <div class="stat-number" id="totalCount">0</div>
                            <div class="stat-label">总数量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="completedCount">0</div>
                            <div class="stat-label">已完成</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="successCount">0</div>
                            <div class="stat-label">成功</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="errorCount">0</div>
                            <div class="stat-label">失败</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="remainingCount">0</div>
                            <div class="stat-label">剩余</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="estimatedTime">--</div>
                            <div class="stat-label">预计完成</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 结果区域 -->
            <div class="results-section">
                <div class="results-header">
                    <h3>📋 查询结果</h3>
                    <div>
                        <button class="btn btn-secondary" onclick="downloadCSV()" style="padding: 8px 16px; font-size: 0.9em;">
                            📄 下载CSV
                        </button>
                        <button class="btn btn-secondary" onclick="downloadExcel()" style="padding: 8px 16px; font-size: 0.9em; margin-left: 10px;">
                            📊 下载Excel
                        </button>
                    </div>
                </div>
                <div style="overflow-x: auto; max-height: 500px;">
                    <table class="results-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>原始地址</th>
                                <th>邮政编码</th>
                                <th>标准化地址</th>
                                <th>坐标</th>
                                <th>状态</th>
                                <th>数据源</th>
                            </tr>
                        </thead>
                        <tbody id="resultsTableBody">
                            <tr>
                                <td colspan="7" style="text-align: center; padding: 40px; color: #666;">
                                    📝 请输入地址数据并开始批量查询
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 日志区域 -->
            <div class="log-section" id="logSection">
                <div style="font-weight: bold; margin-bottom: 10px;">📝 查询日志:</div>
                <div id="logContent">等待开始查询...</div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let addressList = [];
        let queryResults = [];
        let isQuerying = false;
        let isPaused = false;
        let currentIndex = 0;
        let queryStartTime = null;

        // 🚀 初始化页面
        function initializePage() {
            // 设置文件拖拽功能
            const fileUpload = document.querySelector('.file-upload');
            
            fileUpload.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileUpload.classList.add('dragover');
            });
            
            fileUpload.addEventListener('dragleave', () => {
                fileUpload.classList.remove('dragover');
            });
            
            fileUpload.addEventListener('drop', (e) => {
                e.preventDefault();
                fileUpload.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileUpload({ target: { files } });
                }
            });
        }

        // 📝 解析文本输入
        function parseTextInput() {
            const text = document.getElementById('addressTextarea').value.trim();
            if (!text) {
                showError('请输入地址数据');
                return;
            }

            const lines = text.split('\n').filter(line => line.trim());
            const addresses = [];

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                const parts = line.split(',').map(part => part.trim());
                
                if (parts.length >= 3) {
                    addresses.push({
                        street: parts[0],
                        city: parts[1],
                        state: parts[2],
                        original: line
                    });
                } else {
                    logMessage(`⚠️ 第${i + 1}行格式不正确，已跳过: ${line}`);
                }
            }

            if (addresses.length === 0) {
                showError('没有找到有效的地址格式');
                return;
            }

            addressList = addresses;
            updateAddressList();
            showSuccess(`✅ 成功解析 ${addresses.length} 个地址`);
        }

        // 📁 处理文件上传
        async function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            logMessage(`📁 正在处理文件: ${file.name}`);

            try {
                let addresses = [];
                const fileName = file.name.toLowerCase();

                if (fileName.endsWith('.csv') || fileName.endsWith('.txt')) {
                    addresses = await parseCSVFile(file);
                } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
                    addresses = await parseExcelFile(file);
                } else {
                    throw new Error('不支持的文件格式');
                }

                if (addresses.length === 0) {
                    throw new Error('文件中没有找到有效的地址数据');
                }

                addressList = addresses;
                updateAddressList();
                showSuccess(`✅ 成功从文件中导入 ${addresses.length} 个地址`);
            } catch (error) {
                showError(`文件处理失败: ${error.message}`);
            }
        }

        // 📄 解析CSV文件
        async function parseCSVFile(file) {
            const text = await file.text();
            const lines = text.split('\n').filter(line => line.trim());
            const addresses = [];

            // 跳过标题行
            const dataLines = lines.slice(1);

            for (let i = 0; i < dataLines.length; i++) {
                const line = dataLines[i].trim();
                if (!line) continue;

                // 简单的CSV解析（处理逗号分隔）
                const parts = line.split(',').map(part => part.trim().replace(/"/g, ''));
                
                if (parts.length >= 3) {
                    addresses.push({
                        street: parts[0],
                        city: parts[1],
                        state: parts[2],
                        original: `${parts[0]}, ${parts[1]}, ${parts[2]}`
                    });
                }
            }

            return addresses;
        }

        // 📊 解析Excel文件（简化版本）
        async function parseExcelFile(file) {
            // 注意：这里需要引入xlsx库来处理Excel文件
            // 为了简化，这里只是一个占位符
            throw new Error('Excel文件解析需要额外的库支持，请使用CSV格式');
        }

        // 📋 更新地址列表显示
        function updateAddressList() {
            const tbody = document.getElementById('resultsTableBody');
            tbody.innerHTML = '';

            if (addressList.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 40px; color: #666;">
                            📝 请输入地址数据并开始批量查询
                        </td>
                    </tr>
                `;
                return;
            }

            addressList.forEach((address, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${address.original}</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td class="status-pending">等待查询</td>
                    <td>-</td>
                `;
                tbody.appendChild(row);
            });

            // 更新统计
            document.getElementById('totalCount').textContent = addressList.length;
            document.getElementById('remainingCount').textContent = addressList.length;
        }

        // 🚀 开始批量查询
        async function startBatchQuery() {
            if (addressList.length === 0) {
                showError('请先输入地址数据');
                return;
            }

            isQuerying = true;
            isPaused = false;
            currentIndex = 0;
            queryResults = [];
            queryStartTime = Date.now();

            // 更新按钮状态
            document.getElementById('startBtn').disabled = true;
            document.getElementById('pauseBtn').disabled = false;
            document.getElementById('stopBtn').disabled = false;

            // 显示进度区域
            document.getElementById('progressSection').style.display = 'block';

            logMessage('🚀 开始批量查询...');

            const delay = parseInt(document.getElementById('delayInput').value);
            const maxRetries = parseInt(document.getElementById('retryInput').value);
            const concurrent = parseInt(document.getElementById('concurrentInput').value);

            try {
                if (concurrent === 1) {
                    // 串行查询
                    await processAddressesSequentially(delay, maxRetries);
                } else {
                    // 并发查询
                    await processAddressesConcurrently(concurrent, delay, maxRetries);
                }

                if (!isPaused && isQuerying) {
                    showSuccess('✅ 批量查询完成！');
                    logMessage('✅ 所有地址查询完成');
                }
            } catch (error) {
                showError(`批量查询失败: ${error.message}`);
                logMessage(`❌ 查询失败: ${error.message}`);
            } finally {
                // 重置状态
                isQuerying = false;
                document.getElementById('startBtn').disabled = false;
                document.getElementById('pauseBtn').disabled = true;
                document.getElementById('stopBtn').disabled = true;
            }
        }

        // 📝 串行处理地址
        async function processAddressesSequentially(delay, maxRetries) {
            for (let i = currentIndex; i < addressList.length && isQuerying && !isPaused; i++) {
                currentIndex = i;
                await processAddress(i, maxRetries);
                updateProgress();
                
                if (i < addressList.length - 1 && isQuerying && !isPaused) {
                    await sleep(delay);
                }
            }
        }

        // 🔄 并发处理地址
        async function processAddressesConcurrently(concurrent, delay, maxRetries) {
            const chunks = [];
            for (let i = 0; i < addressList.length; i += concurrent) {
                chunks.push(addressList.slice(i, i + concurrent));
            }

            for (let chunkIndex = 0; chunkIndex < chunks.length && isQuerying && !isPaused; chunkIndex++) {
                const chunk = chunks[chunkIndex];
                const promises = chunk.map((_, index) => {
                    const globalIndex = chunkIndex * concurrent + index;
                    return processAddress(globalIndex, maxRetries);
                });

                await Promise.all(promises);
                updateProgress();

                if (chunkIndex < chunks.length - 1 && isQuerying && !isPaused) {
                    await sleep(delay);
                }
            }
        }

        // 🔍 处理单个地址
        async function processAddress(index, maxRetries) {
            const address = addressList[index];
            const row = document.getElementById('resultsTableBody').children[index];
            
            // 更新状态为查询中
            row.children[5].textContent = '查询中...';
            row.children[5].className = 'status-pending';

            let lastError = null;
            
            for (let retry = 0; retry < maxRetries; retry++) {
                try {
                    const result = await queryAddress(address);
                    
                    if (result) {
                        // 查询成功
                        queryResults[index] = result;
                        updateRowWithResult(row, result, '成功');
                        logMessage(`✅ [${index + 1}/${addressList.length}] 查询成功: ${address.original} -> ${result.zipCode}`);
                        return;
                    }
                } catch (error) {
                    lastError = error;
                    logMessage(`⚠️ [${index + 1}/${addressList.length}] 重试 ${retry + 1}/${maxRetries}: ${error.message}`);
                    
                    if (retry < maxRetries - 1) {
                        await sleep(1000); // 重试前等待1秒
                    }
                }
            }

            // 所有重试都失败
            queryResults[index] = null;
            updateRowWithResult(row, null, `失败: ${lastError?.message || '未知错误'}`);
            logMessage(`❌ [${index + 1}/${addressList.length}] 查询失败: ${address.original}`);
        }

        // 🔍 查询单个地址
        async function queryAddress(address) {
            const apiType = document.getElementById('apiSelect').value;
            const fullAddress = `${address.street}, ${address.city}, ${address.state}, USA`;

            switch (apiType) {
                case 'nominatim':
                    return await queryNominatim(fullAddress);
                case 'geocoding':
                    // 需要API密钥
                    throw new Error('Geocoding API需要密钥配置');
                case 'mapbox':
                    // 需要API密钥
                    throw new Error('MapBox API需要密钥配置');
                default:
                    throw new Error('未知的API类型');
            }
        }

        // 🌍 使用Nominatim API查询
        async function queryNominatim(address) {
            const encodedAddress = encodeURIComponent(address);
            const url = `https://nominatim.openstreetmap.org/search?format=json&addressdetails=1&limit=1&q=${encodedAddress}`;
            
            const response = await fetch(url, {
                headers: {
                    'User-Agent': 'Batch-ZIP-Lookup-Tool/1.0'
                }
            });

            if (!response.ok) {
                throw new Error(`Nominatim API错误: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.length === 0) {
                return null;
            }

            const result = data[0];
            const addressDetails = result.address || {};
            
            return {
                zipCode: addressDetails.postcode || 'N/A',
                address: result.display_name,
                source: 'Nominatim',
                latitude: result.lat,
                longitude: result.lon,
                confidence: result.importance
            };
        }

        // 📊 更新表格行结果
        function updateRowWithResult(row, result, status) {
            if (result) {
                row.children[2].textContent = result.zipCode;
                row.children[3].textContent = result.address;
                row.children[4].textContent = `${result.latitude}, ${result.longitude}`;
                row.children[5].textContent = status;
                row.children[5].className = 'status-success';
                row.children[6].textContent = result.source;
            } else {
                row.children[2].textContent = '-';
                row.children[3].textContent = '-';
                row.children[4].textContent = '-';
                row.children[5].textContent = status;
                row.children[5].className = 'status-error';
                row.children[6].textContent = '-';
            }
        }

        // 📊 更新进度
        function updateProgress() {
            const completed = currentIndex + 1;
            const total = addressList.length;
            const success = queryResults.filter(r => r !== null && r !== undefined).length;
            const errors = queryResults.filter(r => r === null).length;
            const remaining = total - completed;
            
            // 更新进度条
            const percentage = (completed / total) * 100;
            document.getElementById('progressFill').style.width = `${percentage}%`;
            
            // 更新统计
            document.getElementById('completedCount').textContent = completed;
            document.getElementById('successCount').textContent = success;
            document.getElementById('errorCount').textContent = errors;
            document.getElementById('remainingCount').textContent = remaining;
            
            // 计算预计完成时间
            if (completed > 0 && remaining > 0) {
                const elapsed = Date.now() - queryStartTime;
                const avgTime = elapsed / completed;
                const estimatedRemaining = avgTime * remaining;
                const estimatedFinish = new Date(Date.now() + estimatedRemaining);
                document.getElementById('estimatedTime').textContent = estimatedFinish.toLocaleTimeString();
            } else {
                document.getElementById('estimatedTime').textContent = '--';
            }
        }

        // ⏸️ 暂停查询
        function pauseQuery() {
            isPaused = true;
            logMessage('⏸️ 查询已暂停');
            document.getElementById('pauseBtn').disabled = true;
            document.getElementById('startBtn').disabled = false;
        }

        // ⏹️ 停止查询
        function stopQuery() {
            isQuerying = false;
            isPaused = false;
            logMessage('⏹️ 查询已停止');
            
            // 重置按钮状态
            document.getElementById('startBtn').disabled = false;
            document.getElementById('pauseBtn').disabled = true;
            document.getElementById('stopBtn').disabled = true;
        }

        // 🗑️ 清空结果
        function clearResults() {
            addressList = [];
            queryResults = [];
            currentIndex = 0;
            updateAddressList();
            document.getElementById('progressSection').style.display = 'none';
            document.getElementById('logContent').textContent = '等待开始查询...';
            showSuccess('✅ 结果已清空');
        }

        // 💾 导出结果
        function exportResults() {
            if (queryResults.length === 0) {
                showError('没有可导出的结果');
                return;
            }
            
            downloadCSV();
        }

        // 📄 下载CSV
        function downloadCSV() {
            const headers = ['序号', '原始地址', '邮政编码', '标准化地址', '纬度', '经度', '状态', '数据源'];
            const rows = [headers];
            
            addressList.forEach((address, index) => {
                const result = queryResults[index];
                rows.push([
                    index + 1,
                    address.original,
                    result ? result.zipCode : '',
                    result ? result.address : '',
                    result ? result.latitude : '',
                    result ? result.longitude : '',
                    result ? '成功' : '失败',
                    result ? result.source : ''
                ]);
            });
            
            const csvContent = rows.map(row => 
                row.map(cell => `"${cell}"`).join(',')
            ).join('\n');
            
            downloadFile('batch-zip-lookup-results.csv', csvContent, 'text/csv');
        }

        // 📊 下载Excel（简化版本）
        function downloadExcel() {
            // 这里可以集成xlsx库来生成真正的Excel文件
            // 目前使用CSV格式
            downloadCSV();
        }

        // 💾 下载文件
        function downloadFile(filename, content, mimeType) {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 📝 记录日志
        function logMessage(message) {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            logContent.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logContent.scrollTop = logContent.scrollHeight;
        }

        // ⏱️ 延时函数
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // ❌ 显示错误信息
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = `❌ ${message}`;
            document.querySelector('.main-content').insertBefore(errorDiv, document.querySelector('.input-section'));
            setTimeout(() => errorDiv.remove(), 5000);
        }

        // ✅ 显示成功信息
        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;
            document.querySelector('.main-content').insertBefore(successDiv, document.querySelector('.input-section'));
            setTimeout(() => successDiv.remove(), 3000);
        }

        // 🚀 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
