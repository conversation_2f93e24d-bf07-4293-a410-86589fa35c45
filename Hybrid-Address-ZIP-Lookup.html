<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 混合地址邮政编码查询</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .main-content {
            padding: 40px;
        }

        .strategy-notice {
            background: linear-gradient(135deg, #e8f5e8, #f0f8ff);
            border-left: 5px solid #4CAF50;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 0 10px 10px 0;
        }

        .search-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
            font-size: 1.1em;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1.1em;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #FF6B6B;
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(255, 107, 107, 0.3);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .batch-section {
            background: #fff3e0;
            border-left: 5px solid #ff9800;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 0 10px 10px 0;
        }

        .textarea-input {
            width: 100%;
            height: 200px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9em;
            resize: vertical;
            margin-bottom: 15px;
        }

        .results-section {
            background: white;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            overflow: hidden;
        }

        .results-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .result-item {
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 10px;
            border-left: 5px solid #ffc107;
        }

        .result-item.offline {
            background: #e8f5e8;
            border-left-color: #4CAF50;
        }

        .result-item.online {
            background: #e3f2fd;
            border-left-color: #2196F3;
        }

        .result-item.error {
            background: #ffebee;
            border-left-color: #f44336;
        }

        .zip-code {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .zip-code.offline {
            color: #2e7d32;
        }

        .zip-code.online {
            color: #1976d2;
        }

        .zip-code.error {
            color: #c62828;
        }

        .address-info {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .source-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .source-badge.offline {
            background: #4CAF50;
            color: white;
        }

        .source-badge.online {
            background: #2196F3;
            color: white;
        }

        .source-badge.error {
            background: #f44336;
            color: white;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-copy {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .btn-copy:hover {
            background: #138496;
        }

        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #f44336;
            margin: 15px 0;
        }

        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
            margin: 15px 0;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #FF6B6B;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #FF6B6B, #4ECDC4);
            width: 0%;
            transition: width 0.3s ease;
        }

        .format-selector {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .format-selector label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-weight: normal;
        }

        .format-selector input[type="radio"] {
            margin-right: 8px;
            width: auto;
        }

        .batch-results {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            margin-top: 15px;
        }

        .stats-summary {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-number.offline {
            color: #4CAF50;
        }

        .stat-number.online {
            color: #2196F3;
        }

        .stat-number.error {
            color: #f44336;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .action-buttons {
                flex-direction: column;
            }
            
            .format-selector {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 混合地址邮政编码查询</h1>
            <p>智能混合策略：离线优先 + 在线备用，确保查询成功</p>
        </div>

        <div class="main-content">
            <!-- 策略说明 -->
            <div class="strategy-notice">
                <h3>🧠 智能查询策略</h3>
                <p><strong>🔒 第一步：离线查询</strong> - 从本地数据库快速查找，保护隐私</p>
                <p><strong>🌐 第二步：在线备用</strong> - 离线未找到时，自动调用多个在线API</p>
                <p><strong>✅ 最佳结果</strong> - 结合速度、隐私和准确性的完美平衡</p>
            </div>

            <!-- 单个地址查询 -->
            <div class="search-section">
                <h3 style="margin-bottom: 20px; color: #333;">🔍 单个地址查询</h3>
                
                <div class="form-group">
                    <label for="fullAddress">🏠 完整地址</label>
                    <input type="text" id="fullAddress" placeholder="例如: 1600 Pennsylvania Avenue NW, Washington, DC">
                </div>
                
                <button class="btn" onclick="searchSingleAddress()" id="searchBtn">
                    🔍 智能查询
                </button>
            </div>

            <!-- 批量查询区域 -->
            <div class="batch-section">
                <h3>📦 批量地址查询</h3>
                <p style="margin-bottom: 15px;">支持多种导入格式：</p>
                
                <!-- 格式选择 -->
                <div class="format-selector">
                    <label>
                        <input type="radio" name="importFormat" value="line" checked>
                        <span>📝 每行一个地址</span>
                    </label>
                    <label>
                        <input type="radio" name="importFormat" value="tab">
                        <span>📊 Tab分隔格式</span>
                    </label>
                    <label>
                        <input type="radio" name="importFormat" value="comma">
                        <span>📋 逗号分隔格式</span>
                    </label>
                </div>

                <textarea class="textarea-input" id="batchInput" placeholder="请输入地址列表..."></textarea>
                
                <button class="btn" onclick="batchSearch()" id="batchBtn">
                    🚀 批量智能查询
                </button>
                
                <div class="progress-bar" id="progressBar" style="display: none;">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                
                <div class="batch-results" id="batchResults" style="display: none;"></div>
            </div>

            <!-- 结果区域 -->
            <div class="results-section">
                <div class="results-header">
                    <h3>📋 查询结果</h3>
                </div>
                <div class="results-content" id="resultsContent" style="padding: 20px;">
                    <div style="text-align: center; padding: 40px; color: #666;">
                        👋 欢迎使用混合地址邮政编码查询工具！<br>
                        输入地址开始智能查询
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 🏠 内置离线地址数据库（常用地址）
        const OFFLINE_DATABASE = [
            // 政府建筑
            { street: "1600 Pennsylvania Avenue NW", city: "Washington", state: "DC", zip: "20500", type: "Government" },
            { street: "1 First Street NE", city: "Washington", state: "DC", zip: "20543", type: "Government" },
            { street: "101 Independence Avenue SE", city: "Washington", state: "DC", zip: "20540", type: "Government" },
            
            // 纽约地标
            { street: "350 Fifth Avenue", city: "New York", state: "NY", zip: "10118", type: "Commercial" },
            { street: "1 Wall Street", city: "New York", state: "NY", zip: "10005", type: "Financial" },
            { street: "Times Square", city: "New York", state: "NY", zip: "10036", type: "Tourist" },
            { street: "1 World Trade Center", city: "New York", state: "NY", zip: "10007", type: "Commercial" },
            { street: "30 Rockefeller Plaza", city: "New York", state: "NY", zip: "10112", type: "Commercial" },
            
            // 科技公司
            { street: "1 Infinite Loop", city: "Cupertino", state: "CA", zip: "95014", type: "Technology" },
            { street: "1600 Amphitheatre Parkway", city: "Mountain View", state: "CA", zip: "94043", type: "Technology" },
            { street: "1 Hacker Way", city: "Menlo Park", state: "CA", zip: "94025", type: "Technology" },
            { street: "410 Terry Avenue North", city: "Seattle", state: "WA", zip: "98109", type: "Technology" },
            { street: "1 Microsoft Way", city: "Redmond", state: "WA", zip: "98052", type: "Technology" },
            
            // 其他主要城市
            { street: "233 S Wacker Drive", city: "Chicago", state: "IL", zip: "60606", type: "Commercial" },
            { street: "1200 Biscayne Boulevard", city: "Miami", state: "FL", zip: "33132", type: "Commercial" },
            { street: "1500 Market Street", city: "Philadelphia", state: "PA", zip: "19102", type: "Commercial" },
            { street: "101 Marietta Street NW", city: "Atlanta", state: "GA", zip: "30303", type: "Commercial" },
            { street: "1600 Broadway", city: "Denver", state: "CO", zip: "80202", type: "Commercial" },
            { street: "3799 Las Vegas Boulevard South", city: "Las Vegas", state: "NV", zip: "89109", type: "Entertainment" }
        ];

        // 🌐 在线API配置
        const API_CONFIGS = [
            {
                name: "USPS API",
                url: "https://api.zippopotam.us/us/",
                type: "reverse", // 这个API是反向查询，我们需要适配
                enabled: true
            },
            {
                name: "Geocoding API",
                url: "https://nominatim.openstreetmap.org/search",
                type: "forward",
                enabled: true
            }
        ];

        let currentBatchResults = [];
        let batchProgress = { total: 0, completed: 0, offline: 0, online: 0, failed: 0 };

        // 🔍 单个地址查询
        async function searchSingleAddress() {
            const address = document.getElementById('fullAddress').value.trim();
            if (!address) {
                showError('请输入完整地址');
                return;
            }

            const btn = document.getElementById('searchBtn');
            btn.disabled = true;
            btn.innerHTML = '<span class="loading"></span>正在查询...';

            try {
                const result = await hybridAddressLookup(address);
                showSingleResult(result, address);
            } catch (error) {
                showError(`查询失败: ${error.message}`);
            } finally {
                btn.disabled = false;
                btn.innerHTML = '🔍 智能查询';
            }
        }

        // 🔄 混合查询核心函数
        async function hybridAddressLookup(address) {
            // 第一步：尝试离线查询
            const offlineResult = searchOfflineDatabase(address);
            if (offlineResult) {
                return {
                    ...offlineResult,
                    source: 'offline',
                    confidence: 'high'
                };
            }

            // 第二步：在线API查询
            try {
                const onlineResult = await searchOnlineAPIs(address);
                return {
                    ...onlineResult,
                    source: 'online',
                    confidence: 'medium'
                };
            } catch (error) {
                throw new Error('离线和在线查询都失败了');
            }
        }

        // 🔒 离线数据库查询
        function searchOfflineDatabase(address) {
            const searchText = address.toLowerCase();
            
            for (const addr of OFFLINE_DATABASE) {
                const fullAddr = `${addr.street}, ${addr.city}, ${addr.state}`.toLowerCase();
                
                // 完整匹配
                if (fullAddr.includes(searchText) || searchText.includes(fullAddr)) {
                    return {
                        zip: addr.zip,
                        street: addr.street,
                        city: addr.city,
                        state: addr.state,
                        type: addr.type
                    };
                }
                
                // 部分匹配
                const searchWords = searchText.split(/[,\s]+/).filter(w => w.length > 2);
                const addrWords = fullAddr.split(/[,\s]+/).filter(w => w.length > 2);
                
                let matchCount = 0;
                searchWords.forEach(searchWord => {
                    addrWords.forEach(addrWord => {
                        if (addrWord.includes(searchWord) || searchWord.includes(addrWord)) {
                            matchCount++;
                        }
                    });
                });
                
                if (matchCount >= 2) {
                    return {
                        zip: addr.zip,
                        street: addr.street,
                        city: addr.city,
                        state: addr.state,
                        type: addr.type
                    };
                }
            }
            
            return null;
        }

        // 🌐 在线API查询
        async function searchOnlineAPIs(address) {
            // 尝试使用Nominatim API
            try {
                const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&addressdetails=1&limit=1&q=${encodeURIComponent(address)}`);
                const data = await response.json();
                
                if (data && data.length > 0) {
                    const result = data[0];
                    const addressComponents = result.address;
                    
                    if (addressComponents && addressComponents.postcode) {
                        return {
                            zip: addressComponents.postcode,
                            street: addressComponents.house_number && addressComponents.road ? 
                                   `${addressComponents.house_number} ${addressComponents.road}` : 
                                   (addressComponents.road || ''),
                            city: addressComponents.city || addressComponents.town || addressComponents.village || '',
                            state: addressComponents.state || '',
                            type: 'Online'
                        };
                    }
                }
            } catch (error) {
                console.error('Nominatim API error:', error);
            }

            // 如果Nominatim失败，可以尝试其他API
            throw new Error('所有在线API都无法找到该地址');
        }

        // 📊 显示单个查询结果
        function showSingleResult(result, originalAddress) {
            const resultsContent = document.getElementById('resultsContent');
            const sourceClass = result.source;
            const sourceText = result.source === 'offline' ? '🔒 离线数据库' : '🌐 在线API';
            const sourceBadge = result.source === 'offline' ? 'offline' : 'online';
            
            resultsContent.innerHTML = `
                <div class="result-item ${sourceClass}">
                    <div class="source-badge ${sourceBadge}">${sourceText}</div>
                    <div class="zip-code ${sourceClass}">📮 ${result.zip}</div>
                    <div class="address-info">
                        <strong>🏠 查询地址:</strong> ${originalAddress}<br>
                        <strong>📍 匹配地址:</strong> ${result.street}, ${result.city}, ${result.state}<br>
                        <strong>🏷️ 地址类型:</strong> ${result.type}<br>
                        <strong>📊 数据源:</strong> ${sourceText}<br>
                        <strong>🎯 可信度:</strong> ${result.confidence === 'high' ? '🟢 高' : '🟡 中等'}
                    </div>
                    <div class="action-buttons">
                        <button class="btn-copy" onclick="copyToClipboard('${result.zip}')">
                            📋 复制邮政编码
                        </button>
                        <button class="btn-copy" onclick="copyToClipboard('${result.street}')">
                            📋 复制地址
                        </button>
                        <button class="btn-copy" onclick="copyToClipboard('${result.street}, ${result.city}, ${result.state} ${result.zip}')">
                            📋 复制完整信息
                        </button>
                    </div>
                    <div style="margin-top: 15px; padding: 10px; background: ${result.source === 'offline' ? '#e8f5e8' : '#e3f2fd'}; border-radius: 5px; font-size: 0.9em; border-left: 4px solid ${result.source === 'offline' ? '#4CAF50' : '#2196F3'};">
                        <strong>${result.source === 'offline' ? '🔒 离线查询' : '🌐 在线查询'}:</strong> 
                        ${result.source === 'offline' ? '此结果来自本地数据库，无需网络连接，保护您的隐私。' : '此结果来自在线API，数据更全面但需要网络连接。'}
                    </div>
                </div>
            `;
        }

        // 📦 批量查询
        async function batchSearch() {
            const input = document.getElementById('batchInput').value.trim();
            if (!input) {
                showError('请输入要查询的地址列表');
                return;
            }

            const btn = document.getElementById('batchBtn');
            btn.disabled = true;
            btn.innerHTML = '<span class="loading"></span>正在批量查询...';

            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            const batchResults = document.getElementById('batchResults');
            
            progressBar.style.display = 'block';
            batchResults.style.display = 'block';
            
            // 重置进度
            batchProgress = { total: 0, completed: 0, offline: 0, online: 0, failed: 0 };
            currentBatchResults = [];

            try {
                const format = document.querySelector('input[name="importFormat"]:checked').value;
                const addresses = parseAddresses(input, format);
                batchProgress.total = addresses.length;

                // 显示初始统计
                updateBatchStats();

                // 逐个处理地址
                for (let i = 0; i < addresses.length; i++) {
                    const address = addresses[i];
                    try {
                        const result = await hybridAddressLookup(address);
                        currentBatchResults.push({
                            index: i + 1,
                            original: address,
                            result: result,
                            success: true
                        });
                        
                        if (result.source === 'offline') {
                            batchProgress.offline++;
                        } else {
                            batchProgress.online++;
                        }
                    } catch (error) {
                        currentBatchResults.push({
                            index: i + 1,
                            original: address,
                            error: error.message,
                            success: false
                        });
                        batchProgress.failed++;
                    }
                    
                    batchProgress.completed++;
                    
                    // 更新进度
                    const progress = (batchProgress.completed / batchProgress.total) * 100;
                    progressFill.style.width = `${progress}%`;
                    
                    // 更新统计和结果
                    updateBatchStats();
                    updateBatchResults();
                    
                    // 添加小延迟避免API限制
                    if (i < addresses.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                }

            } catch (error) {
                showError(`批量查询失败: ${error.message}`);
            } finally {
                btn.disabled = false;
                btn.innerHTML = '🚀 批量智能查询';
                progressBar.style.display = 'none';
            }
        }

        // 📋 解析地址列表
        function parseAddresses(input, format) {
            const lines = input.split('\n').filter(line => line.trim());
            const addresses = [];

            lines.forEach(line => {
                const trimmedLine = line.trim();
                if (!trimmedLine) return;

                if (format === 'tab') {
                    const parts = trimmedLine.split('\t');
                    if (parts.length >= 3) {
                        addresses.push(`${parts[0].trim()}, ${parts[1].trim()}, ${parts[2].trim()}`);
                    } else {
                        addresses.push(trimmedLine);
                    }
                } else if (format === 'comma') {
                    const parts = parseCSVLine(trimmedLine);
                    if (parts.length >= 3) {
                        addresses.push(`${parts[0].trim()}, ${parts[1].trim()}, ${parts[2].trim()}`);
                    } else {
                        addresses.push(trimmedLine);
                    }
                } else {
                    addresses.push(trimmedLine);
                }
            });

            return addresses;
        }

        // 📋 解析CSV行
        function parseCSVLine(line) {
            const result = [];
            let current = '';
            let inQuotes = false;
            
            for (let i = 0; i < line.length; i++) {
                const char = line[i];
                
                if (char === '"') {
                    inQuotes = !inQuotes;
                } else if (char === ',' && !inQuotes) {
                    result.push(current);
                    current = '';
                } else {
                    current += char;
                }
            }
            
            result.push(current);
            return result;
        }

        // 📊 更新批量统计
        function updateBatchStats() {
            const batchResults = document.getElementById('batchResults');
            const successCount = batchProgress.offline + batchProgress.online;
            
            const statsHTML = `
                <div class="stats-summary">
                    <div class="stat-item">
                        <div class="stat-number">${batchProgress.completed}/${batchProgress.total}</div>
                        <div class="stat-label">已完成</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number offline">${batchProgress.offline}</div>
                        <div class="stat-label">离线成功</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number online">${batchProgress.online}</div>
                        <div class="stat-label">在线成功</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number error">${batchProgress.failed}</div>
                        <div class="stat-label">查询失败</div>
                    </div>
                </div>
            `;
            
            // 如果还没有结果区域，创建一个
            if (!batchResults.querySelector('.stats-summary')) {
                batchResults.innerHTML = statsHTML + '<div id="batchResultsList"></div>';
            } else {
                batchResults.querySelector('.stats-summary').outerHTML = statsHTML;
            }
        }

        // 📊 更新批量结果
        function updateBatchResults() {
            const resultsList = document.getElementById('batchResultsList');
            if (!resultsList) return;

            let html = '';
            
            currentBatchResults.forEach(item => {
                if (item.success) {
                    const result = item.result;
                    const sourceClass = result.source;
                    const sourceText = result.source === 'offline' ? '🔒 离线' : '🌐 在线';
                    
                    html += `
                        <div style="margin-bottom: 10px; padding: 15px; background: ${result.source === 'offline' ? '#e8f5e8' : '#e3f2fd'}; border-radius: 5px; border-left: 3px solid ${result.source === 'offline' ? '#4CAF50' : '#2196F3'};">
                            <strong>${item.index}. ${item.original}</strong><br>
                            📮 <strong>${result.zip}</strong> - ${result.street}, ${result.city}, ${result.state}<br>
                            <small style="color: #666;">${sourceText} | 类型: ${result.type}</small>
                        </div>
                    `;
                } else {
                    html += `
                        <div style="margin-bottom: 10px; padding: 15px; background: #ffebee; border-radius: 5px; border-left: 3px solid #f44336;">
                            <strong>${item.index}. ${item.original}</strong><br>
                            ❌ ${item.error}
                        </div>
                    `;
                }
            });
            
            resultsList.innerHTML = html;
        }

        // 📋 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showSuccess(`✅ 已复制: ${text}`);
            }).catch(err => {
                console.error('复制失败:', err);
                // 备用方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showSuccess(`✅ 已复制: ${text}`);
            });
        }

        // ❌ 显示错误信息
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = `❌ ${message}`;
            document.querySelector('.main-content').insertBefore(errorDiv, document.querySelector('.search-section'));
            setTimeout(() => errorDiv.remove(), 5000);
        }

        // ✅ 显示成功信息
        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;
            document.querySelector('.main-content').insertBefore(successDiv, document.querySelector('.search-section'));
            setTimeout(() => successDiv.remove(), 3000);
        }

        // 🚀 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 添加格式切换事件监听
            const formatRadios = document.querySelectorAll('input[name="importFormat"]');
            formatRadios.forEach(radio => {
                radio.addEventListener('change', () => {
                    const format = radio.value;
                    const textarea = document.getElementById('batchInput');
                    
                    if (format === 'line') {
                        textarea.placeholder = '请输入地址列表，每行一个地址...';
                    } else if (format === 'tab') {
                        textarea.placeholder = '请粘贴Tab分隔的地址数据（从Excel复制）...';
                    } else if (format === 'comma') {
                        textarea.placeholder = '请输入逗号分隔的地址数据...';
                    }
                });
            });
        });
    </script>
</body>
</html>
