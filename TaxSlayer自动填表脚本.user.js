// ==UserScript==
// @name         TaxSlayer自动填表脚本
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动填写TaxSlayer表单
// <AUTHOR>
// @match        https://www.taxslayer.com/*
// @match        https://*.taxslayer.com/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @connect      api.sms8.net
// @connect      *
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // 🎯 用户数据配置
    const userData = {
        email: '<EMAIL>',
        username: 'Jtoduf8146',
        password: 'Gsdcuzeqdmif344@',
        phone: '2272282433',
        smsApi: 'https://api.sms8.net/api/record?token=bz2is922192phwxxt75nxvuqr3omq7etlvum',
        firstName: 'AUSTIN',
        lastName: 'DONNER',
        ssn: '*********',
        birthDate: '10/06/1977',
        address: '2120 CABIN BRANCH',
        zipCode: '21771',
        city: 'LUTHERVILLE TIMONIUM',
        state: 'Maryland',
        employerEIN: '41-1366075',
        employerName: 'ALLIANZ LIFE INSURAN',
        employerAddress: '5701 GOLDEN HILLS DR',
        employerZip: '55416',
        wages: '51029',
        federalTax: '5613.19',
        socialSecurityWages: '51029',
        socialSecurityTax: '3163.798',
        medicareWages: '51029',
        medicareTax: '739.9205',
        stateWages: '51029',
        stateTax: '3827.175',
        bankName: 'COMMUNITY FEDERAL SAVINGS BANK',
        routingNumber: '*********',
        accountNumber: '************'
    };

    // 🔧 工具函数
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function check() {
                const element = document.querySelector(selector);
                if (element) {
                    console.log(`✅ 找到元素: ${selector}`);
                    resolve(element);
                    return;
                }
                
                if (Date.now() - startTime > timeout) {
                    console.log(`❌ 元素未找到: ${selector}`);
                    reject(new Error(`Element not found: ${selector}`));
                    return;
                }
                
                setTimeout(check, 100);
            }
            
            check();
        });
    }

    // 模拟真实输入
    async function simulateTyping(element, text) {
        if (!element) return;
        
        element.focus();
        element.value = '';
        
        // 逐字符输入
        for (let i = 0; i < text.length; i++) {
            element.value += text[i];
            element.dispatchEvent(new Event('input', { bubbles: true }));
            await new Promise(resolve => setTimeout(resolve, 50));
        }
        
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        console.log(`📝 已输入: ${text}`);
    }

    // 点击元素
    function clickElement(element) {
        if (!element) return false;
        
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        setTimeout(() => {
            element.click();
            console.log(`🖱️ 已点击: ${element.tagName}`);
        }, 500);
        return true;
    }

    // 🎨 创建导入按钮
    function createImportButton() {
        // 检查是否已存在按钮
        if (document.getElementById('taxslayer-import-btn')) return;

        const button = document.createElement('button');
        button.id = 'taxslayer-import-btn';
        button.innerHTML = '📋 导入资料';
        button.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        `;

        // 悬停效果
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'scale(1.05)';
            button.style.boxShadow = '0 6px 12px rgba(0,0,0,0.3)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.transform = 'scale(1)';
            button.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
        });

        // 点击事件
        button.addEventListener('click', handleImportClick);

        // 如果在验证页面，添加获取验证码按钮
        if (window.location.href.includes('VerifyPhone')) {
            button.innerHTML = '📲 获取验证码';
            button.addEventListener('click', getSMSVerificationCode);
        }

        document.body.appendChild(button);
        console.log('🎯 导入按钮已创建');
    }

    // 📋 处理导入点击事件
    async function handleImportClick() {
        console.log('🚀 开始自动填表...');
        
        try {
            // 检测当前页面类型并执行相应操作
            if (window.location.href.includes('Register')) {
                await fillRegistrationForm();
            } else if (window.location.href.includes('Login')) {
                await fillLoginForm();
            } else if (window.location.href.includes('VerifyPhone')) {
                await handlePhoneVerification();
            } else if (window.location.href.includes('Congrats')) {
                await handleCongratsPage();
            } else if (window.location.href.includes('packages')) {
                await handlePackageSelection();
            } else if (window.location.href.includes('personalinfo')) {
                await handlePersonalInfoPage();
            } else if (window.location.href.includes('Address')) {
                await handleAddressPage();
            } else if (window.location.href.includes('Additional')) {
                await handleAdditionalInfoPage();
            } else if (window.location.href.includes('filingStatus')) {
                await handleFilingStatusPage();
            } else if (window.location.href.includes('DependentsGuide')) {
                await handleDependentsPage();
            } else if (window.location.href.includes('IPPinCheck')) {
                await handleIPPinPage();
            } else if (window.location.href.includes('RegistrationSummary')) {
                await handleRegistrationSummaryPage();
            } else if (document.querySelector('h1')?.textContent?.includes('Quick File')) {
                await handleQuickFilePage();
            } else {
                console.log('📍 当前页面:', window.location.href);
                alert('当前页面暂不支持自动填表');
            }
        } catch (error) {
            console.error('❌ 填表出错:', error);
            alert('填表过程中出现错误，请查看控制台');
        }
    }

    // 📝 填写注册表单
    async function fillRegistrationForm() {
        console.log('📝 开始填写注册表单...');

        try {
            // 第一步：填写邮箱地址
            console.log('📧 填写邮箱地址...');
            const emailInput = await waitForElement('textbox[name="Email"], input[type="email"], [role="textbox"]');
            await simulateTyping(emailInput, userData.email);
            await new Promise(resolve => setTimeout(resolve, 500));

            // 第二步：填写用户名
            console.log('👤 填写用户名...');
            const usernameInput = await waitForElement('textbox[name="Username"], input[name*="username"]');
            await simulateTyping(usernameInput, userData.username);
            await new Promise(resolve => setTimeout(resolve, 500));

            // 第三步：填写密码
            console.log('🔒 填写密码...');
            const passwordInput = await waitForElement('textbox[name="Password"], input[type="password"]');
            await simulateTyping(passwordInput, userData.password);
            await new Promise(resolve => setTimeout(resolve, 500));

            // 第三步半：填写确认密码（如果存在）
            try {
                console.log('🔒 填写确认密码...');
                const confirmPasswordInput = await waitForElement('textbox[name*="Confirm"], input[name*="confirm"], textbox[placeholder*="confirm"]', 2000);
                await simulateTyping(confirmPasswordInput, userData.password);
                await new Promise(resolve => setTimeout(resolve, 500));
                console.log('✅ 确认密码已填写');
            } catch (error) {
                console.log('ℹ️ 未找到确认密码字段，跳过');
            }

            // 第四步：填写手机号
            console.log('📱 填写手机号...');
            const phoneInput = await waitForElement('textbox[name*="Phone"], input[name*="phone"]');
            await simulateTyping(phoneInput, userData.phone);
            await new Promise(resolve => setTimeout(resolve, 1000));

            console.log('✅ 注册表单填写完成');

            // 检查Create Account按钮是否可用
            const createButton = document.querySelector('button[type="submit"], button:contains("Create Account")');
            if (createButton && !createButton.disabled) {
                console.log('🎯 Create Account按钮已启用，可以提交');
                alert('✅ 注册信息已填写完成！Create Account按钮已启用，您可以点击提交了！');
            } else {
                console.log('⏳ Create Account按钮仍然禁用，请检查表单');
                alert('📝 注册信息已填写完成！请检查表单验证后点击"Create Account"按钮');
            }

        } catch (error) {
            console.error('❌ 注册表单填写失败:', error);
            alert('❌ 注册表单填写失败: ' + error.message);
        }
    }

    // 🔑 填写登录表单
    async function fillLoginForm() {
        console.log('🔑 开始填写登录表单...');
        
        try {
            const usernameInput = await waitForElement('input[type="email"], input[name*="username"], input[name*="email"]');
            await simulateTyping(usernameInput, userData.email);
            
            const passwordInput = await waitForElement('input[type="password"]');
            await simulateTyping(passwordInput, userData.password);
            
            console.log('✅ 登录表单填写完成');
            alert('登录信息已填写完成！');
            
        } catch (error) {
            console.error('❌ 登录表单填写失败:', error);
        }
    }

    // � 处理手机验证页面
    async function handlePhoneVerification() {
        console.log('📱 开始处理手机验证页面...');

        try {
            // 检查是否在验证页面
            const pageTitle = document.querySelector('h1');
            if (!pageTitle || !pageTitle.textContent.includes('Check your phone')) {
                throw new Error('不是手机验证页面');
            }

            console.log('📱 确认在手机验证页面');
            alert('📱 已进入手机验证页面！\n\n请手动获取短信验证码并输入，或者等待自动获取功能完善。');

            // 获取短信验证码 (使用提供的API)
            await getSMSVerificationCode();

        } catch (error) {
            console.error('❌ 手机验证处理失败:', error);
            alert('❌ 手机验证处理失败: ' + error.message);
        }
    }

    // 📲 获取短信验证码
    async function getSMSVerificationCode() {
        console.log('📲 尝试获取短信验证码...');

        try {
            // 使用GM_xmlhttpRequest获取短信
            GM_xmlhttpRequest({
                method: 'GET',
                url: userData.smsApi,
                onload: function(response) {
                    console.log('📲 短信API响应:', response.responseText);

                    try {
                        const data = JSON.parse(response.responseText);

                        // 新的API响应格式处理
                        if (data && data.code === 1 && data.data && data.data.code) {
                            // 从返回的消息中提取验证码
                            const message = data.data.code;
                            const codeMatch = message.match(/\b\d{6}\b/);

                            if (codeMatch) {
                                const verificationCode = codeMatch[0];
                                console.log('✅ 找到验证码:', verificationCode);
                                fillVerificationCode(verificationCode);
                            } else {
                                console.log('❌ 未在短信中找到6位验证码');
                                alert('未找到验证码，请手动输入');
                            }
                        }
                        // 兼容旧格式
                        else if (data && data.messages && data.messages.length > 0) {
                            const latestMessage = data.messages[0];
                            const codeMatch = latestMessage.content.match(/\b\d{6}\b/);

                            if (codeMatch) {
                                const verificationCode = codeMatch[0];
                                console.log('✅ 找到验证码:', verificationCode);
                                fillVerificationCode(verificationCode);
                            } else {
                                console.log('❌ 未在短信中找到6位验证码');
                                alert('未找到验证码，请手动输入');
                            }
                        } else {
                            console.log('❌ 没有收到短信或API格式不匹配');
                            console.log('API响应:', data);
                            alert('暂未收到短信，请稍后重试或手动输入验证码');
                        }
                    } catch (parseError) {
                        console.error('❌ 解析短信API响应失败:', parseError);
                        alert('短信API响应解析失败，请手动输入验证码');
                    }
                },
                onerror: function(error) {
                    console.error('❌ 短信API请求失败:', error);
                    alert('短信API请求失败，请手动输入验证码');
                }
            });

        } catch (error) {
            console.error('❌ 获取短信验证码失败:', error);
            alert('获取验证码失败，请手动输入');
        }
    }

    // 📝 填写验证码
    async function fillVerificationCode(code) {
        try {
            console.log('📝 填写验证码:', code);

            // 查找验证码输入框
            const codeInput = await waitForElement('textbox[name*="code"], input[type="text"], textbox[placeholder*="digit"]');
            await simulateTyping(codeInput, code);

            // 等待一下然后点击验证按钮
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 查找验证按钮
            const verifyButton = document.querySelector('button[type="submit"]') ||
                                document.querySelector('button:contains("Verify")') ||
                                document.querySelector('button[name*="verify"]') ||
                                document.querySelector('button');

            if (verifyButton) {
                clickElement(verifyButton);
                console.log('✅ 验证码已提交');
                alert('✅ 验证码已自动填写并提交！');

                // 等待页面跳转
                await new Promise(resolve => setTimeout(resolve, 3000));
                console.log('🎉 验证完成，页面已跳转');
            } else {
                console.log('⚠️ 未找到验证按钮，请手动点击');
                alert('验证码已填写，请手动点击"Verify"按钮');
            }

        } catch (error) {
            console.error('❌ 填写验证码失败:', error);
            alert('填写验证码失败: ' + error.message);
        }
    }

    // �📄 处理Quick File页面
    async function handleQuickFilePage() {
        console.log('📄 处理Quick File页面...');

        try {
            // 查找搜索框
            const searchInput = await waitForElement('input[type="text"], input[type="search"], textbox');
            await simulateTyping(searchInput, 'W-2');

            // 等待搜索结果
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 查找W-2选项
            const w2Option = await waitForElement('li, option, div[role="option"]');
            clickElement(w2Option);

            console.log('✅ Quick File页面处理完成');

        } catch (error) {
            console.error('❌ Quick File页面处理失败:', error);
        }
    }

    // 🚀 初始化脚本
    function init() {
        console.log('🎯 TaxSlayer自动填表脚本已启动');
        
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', createImportButton);
        } else {
            createImportButton();
        }
        
        // 监听页面变化
        const observer = new MutationObserver(() => {
            if (!document.getElementById('taxslayer-import-btn')) {
                createImportButton();
            }
        });
        
        observer.observe(document.body, { childList: true, subtree: true });
    }

    // 🎉 处理欢迎页面
    async function handleCongratsPage() {
        console.log('🎉 处理欢迎页面...');

        try {
            // 检查是否在欢迎页面
            const pageTitle = document.querySelector('h1');
            if (!pageTitle || !pageTitle.textContent.includes('Get ready to slay it!')) {
                throw new Error('不是欢迎页面');
            }

            console.log('🎉 确认在欢迎页面');
            alert('🎉 注册和验证成功！现在在欢迎页面。\n\n点击"Keep Going"继续下一步。');

            // 查找并点击Keep Going按钮
            const keepGoingButton = document.querySelector('button:contains("Keep Going")') ||
                                   document.querySelector('button[type="submit"]') ||
                                   document.querySelector('button');

            if (keepGoingButton) {
                clickElement(keepGoingButton);
                console.log('✅ 已点击Keep Going按钮');
            } else {
                console.log('⚠️ 未找到Keep Going按钮');
                alert('请手动点击"Keep Going"按钮继续');
            }

        } catch (error) {
            console.error('❌ 欢迎页面处理失败:', error);
            alert('❌ 欢迎页面处理失败: ' + error.message);
        }
    }

    // 📦 处理套餐选择页面
    async function handlePackageSelection() {
        console.log('📦 处理套餐选择页面...');

        try {
            console.log('📦 确认在套餐选择页面');
            alert('📦 在套餐选择页面！\n\n将自动选择Simply Free套餐（免费）。');

            // 查找Simply Free套餐的按钮
            const simplyFreeButton = document.querySelector('button:contains("Start for Free")') ||
                                   document.querySelector('[data-package="simply-free"] button') ||
                                   document.querySelector('.package-free button');

            if (simplyFreeButton) {
                clickElement(simplyFreeButton);
                console.log('✅ 已选择Simply Free套餐');
                alert('✅ 已选择Simply Free套餐（免费）！');
            } else {
                console.log('⚠️ 未找到Simply Free按钮');
                alert('请手动选择Simply Free套餐');
            }

        } catch (error) {
            console.error('❌ 套餐选择处理失败:', error);
            alert('❌ 套餐选择处理失败: ' + error.message);
        }
    }

    // 👤 处理个人信息页面
    async function handlePersonalInfoPage() {
        console.log('👤 处理个人信息页面...');

        try {
            console.log('👤 确认在个人信息页面');
            alert('👤 在个人信息页面！\n\n将自动填写个人信息。');

            // 填写名字
            const firstNameInput = await waitForElement('textbox[name*="first"], input[name*="first"]');
            await simulateTyping(firstNameInput, userData.firstName);
            await new Promise(resolve => setTimeout(resolve, 500));

            // 填写姓氏
            const lastNameInput = await waitForElement('textbox[name*="last"], input[name*="last"]');
            await simulateTyping(lastNameInput, userData.lastName);
            await new Promise(resolve => setTimeout(resolve, 500));

            // 填写SSN
            const ssnInput = await waitForElement('textbox[name*="ssn"], input[name*="ssn"]');
            await simulateTyping(ssnInput, userData.ssn);
            await new Promise(resolve => setTimeout(resolve, 500));

            // 填写生日
            const birthDateInput = await waitForElement('textbox[name*="birth"], input[name*="birth"]');
            await simulateTyping(birthDateInput, userData.birthDate);
            await new Promise(resolve => setTimeout(resolve, 500));

            // 填写职业
            const occupationInput = await waitForElement('textbox[name*="occupation"], input[name*="occupation"]');
            await simulateTyping(occupationInput, 'Employee');
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 点击Continue按钮
            const continueButton = document.querySelector('button:contains("Continue")') ||
                                 document.querySelector('button[type="submit"]');

            if (continueButton) {
                clickElement(continueButton);
                console.log('✅ 个人信息已填写并提交');
                alert('✅ 个人信息已填写完成！');
            } else {
                console.log('⚠️ 未找到Continue按钮');
                alert('个人信息已填写，请手动点击"Continue"按钮');
            }

        } catch (error) {
            console.error('❌ 个人信息页面处理失败:', error);
            alert('❌ 个人信息页面处理失败: ' + error.message);
        }
    }

    // 🏠 处理地址页面
    async function handleAddressPage() {
        console.log('🏠 处理地址页面...');

        try {
            console.log('🏠 确认在地址页面');
            alert('🏠 在地址页面！\n\n将自动填写地址信息。');

            // 填写地址
            const addressInput = await waitForElement('textbox[name*="address"], input[name*="address"]');
            await simulateTyping(addressInput, userData.address);
            await new Promise(resolve => setTimeout(resolve, 500));

            // 填写邮编
            const zipInput = await waitForElement('textbox[name*="zip"], input[name*="zip"]');
            await simulateTyping(zipInput, userData.zipCode);
            await new Promise(resolve => setTimeout(resolve, 500));

            // 填写城市
            const cityInput = await waitForElement('textbox[name*="city"], input[name*="city"]');
            await simulateTyping(cityInput, userData.city);
            await new Promise(resolve => setTimeout(resolve, 500));

            // 选择州
            const stateSelect = await waitForElement('select[name*="state"], combobox[name*="state"]');
            if (stateSelect) {
                stateSelect.value = 'MD'; // Maryland
                stateSelect.dispatchEvent(new Event('change', { bubbles: true }));
            }
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 点击Continue按钮
            const continueButton = document.querySelector('button:contains("Continue")') ||
                                 document.querySelector('button[type="submit"]');

            if (continueButton) {
                clickElement(continueButton);
                console.log('✅ 地址信息已填写并提交');
                alert('✅ 地址信息已填写完成！');
            } else {
                console.log('⚠️ 未找到Continue按钮');
                alert('地址信息已填写，请手动点击"Continue"按钮');
            }

        } catch (error) {
            console.error('❌ 地址页面处理失败:', error);
            alert('❌ 地址页面处理失败: ' + error.message);
        }
    }

    // ℹ️ 处理附加信息页面
    async function handleAdditionalInfoPage() {
        console.log('ℹ️ 处理附加信息页面...');

        try {
            console.log('ℹ️ 确认在附加信息页面');
            alert('ℹ️ 在附加信息页面！\n\n这些都是可选项，将直接跳过。');

            // 查找Skip按钮
            const skipButton = document.querySelector('button:contains("Skip")') ||
                             document.querySelector('button[type="button"]');

            if (skipButton && skipButton.textContent.includes('Skip')) {
                clickElement(skipButton);
                console.log('✅ 已跳过附加信息');
                alert('✅ 已跳过附加信息页面！');
            } else {
                console.log('⚠️ 未找到Skip按钮，尝试Continue');
                const continueButton = document.querySelector('button:contains("Continue")') ||
                                     document.querySelector('button[type="submit"]');
                if (continueButton) {
                    clickElement(continueButton);
                    console.log('✅ 已点击Continue按钮');
                }
            }

        } catch (error) {
            console.error('❌ 附加信息页面处理失败:', error);
            alert('❌ 附加信息页面处理失败: ' + error.message);
        }
    }

    // 📊 处理申报状态页面
    async function handleFilingStatusPage() {
        console.log('📊 处理申报状态页面...');

        try {
            console.log('📊 确认在申报状态页面');
            alert('📊 在申报状态页面！\n\n将选择Single（单身）申报状态。');

            // 选择Single申报状态
            const singleRadio = document.querySelector('input[name="filingStatus"][value="1"]') ||
                              document.querySelector('input[type="radio"]');

            if (singleRadio) {
                singleRadio.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await new Promise(resolve => setTimeout(resolve, 1000));
                singleRadio.click();
                console.log('✅ 已选择Single申报状态');
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // 点击Continue按钮
            const continueButton = document.querySelector('button:contains("Continue")') ||
                                 document.querySelector('button[type="submit"]');

            if (continueButton) {
                clickElement(continueButton);
                console.log('✅ 申报状态已选择并提交');
                alert('✅ 申报状态已选择完成！');
            } else {
                console.log('⚠️ 未找到Continue按钮');
                alert('申报状态已选择，请手动点击"Continue"按钮');
            }

        } catch (error) {
            console.error('❌ 申报状态页面处理失败:', error);
            alert('❌ 申报状态页面处理失败: ' + error.message);
        }
    }

    // 👨‍👩‍👧‍👦 处理受抚养人页面
    async function handleDependentsPage() {
        console.log('👨‍👩‍👧‍👦 处理受抚养人页面...');

        try {
            console.log('👨‍👩‍👧‍👦 确认在受抚养人页面');
            alert('👨‍👩‍👧‍👦 在受抚养人页面！\n\n将选择"No"（没有受抚养人）。');

            // 选择No（没有受抚养人）
            const noRadio = document.querySelectorAll('input[type="radio"]')[1] ||
                          document.querySelector('input[value="false"]');

            if (noRadio) {
                noRadio.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await new Promise(resolve => setTimeout(resolve, 1000));
                noRadio.click();
                console.log('✅ 已选择No（没有受抚养人）');
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // 点击Continue按钮
            const continueButton = document.querySelector('button:contains("Continue")') ||
                                 document.querySelector('button[type="submit"]');

            if (continueButton) {
                clickElement(continueButton);
                console.log('✅ 受抚养人选择已提交');
                alert('✅ 受抚养人页面已完成！');
            } else {
                console.log('⚠️ 未找到Continue按钮');
                alert('受抚养人已选择，请手动点击"Continue"按钮');
            }

        } catch (error) {
            console.error('❌ 受抚养人页面处理失败:', error);
            alert('❌ 受抚养人页面处理失败: ' + error.message);
        }
    }

    // 🔐 处理身份保护PIN页面
    async function handleIPPinPage() {
        console.log('🔐 处理身份保护PIN页面...');

        try {
            console.log('🔐 确认在身份保护PIN页面');
            alert('🔐 在身份保护PIN页面！\n\n将选择"No"（没有收到身份保护PIN）。');

            // 选择No（没有收到身份保护PIN）
            const noRadio = document.querySelectorAll('input[type="radio"]')[1];

            if (noRadio) {
                noRadio.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await new Promise(resolve => setTimeout(resolve, 1000));
                noRadio.click();
                console.log('✅ 已选择No（没有收到身份保护PIN）');
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // 点击Continue按钮
            const continueButton = document.querySelector('button:contains("Continue")') ||
                                 document.querySelector('button[type="submit"]');

            if (continueButton) {
                clickElement(continueButton);
                console.log('✅ 身份保护PIN选择已提交');
                alert('✅ 身份保护PIN页面已完成！');
            } else {
                console.log('⚠️ 未找到Continue按钮');
                alert('身份保护PIN已选择，请手动点击"Continue"按钮');
            }

        } catch (error) {
            console.error('❌ 身份保护PIN页面处理失败:', error);
            alert('❌ 身份保护PIN页面处理失败: ' + error.message);
        }
    }

    // 📋 处理注册摘要页面
    async function handleRegistrationSummaryPage() {
        console.log('📋 处理注册摘要页面...');

        try {
            console.log('📋 确认在注册摘要页面');
            alert('📋 在注册摘要页面！\n\n所有基本信息已填写完成，将继续下一步。');

            // 查找Continue链接
            const continueLink = document.querySelector('a:contains("Continue")') ||
                               document.querySelector('a[href*="Continue"]') ||
                               document.querySelector('link[href*="Continue"]');

            if (continueLink) {
                clickElement(continueLink);
                console.log('✅ 已点击Continue链接');
                alert('✅ 注册摘要页面已完成！进入下一阶段。');
            } else {
                console.log('⚠️ 未找到Continue链接');
                alert('注册摘要页面已显示，请手动点击"Continue"链接继续');
            }

        } catch (error) {
            console.error('❌ 注册摘要页面处理失败:', error);
            alert('❌ 注册摘要页面处理失败: ' + error.message);
        }
    }

    // 启动脚本
    init();

})();
