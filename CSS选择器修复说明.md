# CSS选择器修复说明

## 🚨 问题描述
脚本在运行时出现CSS选择器语法错误：
```
SyntaxError: Failed to execute 'querySelector' on 'Document': 'div.Quick_File_container, h1:contains("Quick File"), [data-testid*="quick"], [class*="quick"]' is not a valid selector.
```

## 🔍 根本原因
`:contains()` 是jQuery特有的伪选择器，在原生JavaScript的 `querySelector` 和 `querySelectorAll` 中无效。

## ✅ 修复方案

### 1. 创建通用文本查找函数
添加了 `findElementByText()` 函数来处理包含特定文本的元素查找：

```javascript
function findElementByText(selectors, searchText, description = '元素') {
    for (const selector of selectors) {
        try {
            // 如果选择器包含 :contains，则手动处理
            if (selector.includes(':contains')) {
                const baseSelector = selector.split(':contains')[0];
                const elements = document.querySelectorAll(baseSelector);
                const text = selector.match(/\("([^"]+)"\)/)?.[1]?.toLowerCase() || searchText.toLowerCase();
                
                for (const el of elements) {
                    if (el.textContent.toLowerCase().includes(text)) {
                        return el;
                    }
                }
            } else {
                const element = document.querySelector(selector);
                if (element && element.textContent.toLowerCase().includes(searchText.toLowerCase())) {
                    return element;
                }
            }
        } catch (e) {
            console.log(`选择器错误 (${selector}):`, e.message);
        }
    }
    return null;
}
```

### 2. 修复的具体位置

#### A. Quick File页面检测 (第1787行)
**修复前：**
```javascript
waitForElement('div.Quick_File_container, h1:contains("Quick File"), [data-testid*="quick"], [class*="quick"]', function(quickFileElement) {
```

**修复后：**
```javascript
function checkQuickFilePage() {
    // 检查标题元素
    const titleElements = document.querySelectorAll('h1, h2, h3');
    for (const title of titleElements) {
        if (title.textContent.includes('Quick File')) {
            return title;
        }
    }
    
    // 检查其他可能的元素
    const quickFileSelectors = [
        'div.Quick_File_container',
        '[data-testid*="quick"]',
        '[class*="quick"]',
        'div[id*="quick"]'
    ];
    
    for (const selector of quickFileSelectors) {
        try {
            const element = document.querySelector(selector);
            if (element) return element;
        } catch (e) {
            console.log('选择器错误:', selector, e);
        }
    }
    
    return null;
}
```

#### B. Continue按钮查找
修复了多个函数中的Continue按钮选择器：

1. **clickW2ContinueButton()** - 第1235行
2. **clickMarylandContinueButton()** - 第1516行  
3. **findAndClickContinueButton()** - 第1961行

**修复前：**
```javascript
const continueSelectors = [
    'button:contains("Continue")',
    'a:contains("Continue")',
    // ...
];
```

**修复后：**
```javascript
const continueSelectors = [
    'button[type="submit"]',
    'button.btn-primary',
    'button',
    'a'
];

// 然后手动检查文本内容
for (const selector of continueSelectors) {
    const elements = document.querySelectorAll(selector);
    for (const el of elements) {
        if (el.textContent.toLowerCase().includes('continue')) {
            continueButton = el;
            break;
        }
    }
}
```

#### C. W-2表单检测 (第905行)
**修复前：**
```javascript
const w2Indicators = [
    'h1[data-testid="page-title-h1"]',
    'heading:contains("Wage and tax statement")',
    'heading:contains("Form W-2")',
    // ...
];
```

**修复后：**
```javascript
const w2Indicators = [
    'h1[data-testid="page-title-h1"]',
    'heading',
    'region[aria-label*="Wage"]',
    'main'
];
```

#### D. 县选择下拉菜单 (第1353行)
**修复前：**
```javascript
const countySelectors = [
    'select[name*="county"]',
    'select:contains("Select")',
    // ...
];
```

**修复后：**
```javascript
const countySelectors = [
    'select[name*="county"]',
    'combobox[name*="county"]',
    'select',
    'combobox'
];
```

#### E. 其他修复位置
- 第4238行：套餐选择页面的Continue按钮
- 第4491行：通用Continue按钮查找
- 第4553行：首次使用选项查找

## 🎯 修复效果

### ✅ 解决的问题
1. **消除CSS选择器语法错误**
2. **保持原有功能完整性**
3. **提高选择器兼容性**
4. **增强错误处理机制**

### 🔧 改进的功能
1. **更智能的元素查找**：先尝试精确选择器，再回退到通用选择器
2. **更好的错误处理**：捕获选择器错误并记录日志
3. **更灵活的文本匹配**：支持大小写不敏感的文本搜索
4. **向后兼容性**：保持所有原有功能不变

## 📝 使用建议

### 1. 测试验证
建议在以下页面测试脚本：
- Quick File页面
- W-2表单页面  
- Maryland州报税页面
- 各种Continue按钮页面

### 2. 监控日志
注意控制台输出，确认：
- 没有CSS选择器错误
- 元素查找成功
- 按钮点击正常

### 3. 后续维护
如果遇到新的 `:contains` 选择器错误：
1. 使用 `findElementByText()` 函数
2. 或者手动遍历元素检查文本内容
3. 避免直接在 `querySelector` 中使用 `:contains`

## 🎉 总结
所有 `:contains` 选择器已成功修复，脚本现在完全兼容原生JavaScript DOM API，不再依赖jQuery特有的选择器语法。
