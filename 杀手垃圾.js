// ==UserScript==
// @name         杀手半自动填写脚本
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  自动化 TaxSlayer 网站的报税流程
// <AUTHOR>
// @match        https://www.taxslayer.com/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @connect      api.sms8.net
// @connect      sms8.net
// @connect      api.allorigins.win
// @connect      cors-anywhere.herokuapp.com
// @connect      tsgtm.taxslayer.com
// @connect      www.taxslayer.com
// @connect      api.zippopotam.us
// @connect      *
// @require      https://code.jquery.com/jquery-3.6.0.min.js
// @require      https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // 延时处理函数，等待元素加载
    function waitForElement(selector, callback, timeout = 15000) {
        const startTime = Date.now();

        // 使用requestAnimationFrame替代setInterval,性能更好
        function checkElement() {
            const element = document.querySelector(selector);
            const elapsedTime = Date.now() - startTime;

            if (element) {
                console.log(`✅ 找到元素: ${selector}, 耗时: ${elapsedTime}ms`);
                callback(element);
                return;
            }

            if (elapsedTime > timeout) {
                console.log(`❌ 等待元素超时: ${selector}, 已等待: ${timeout}ms`);
                return;
            }

            requestAnimationFrame(checkElement);
        }

        checkElement();
    }

    // 等待Cloudflare验证完成
    async function waitForCloudflare() {
        return new Promise((resolve) => {
            const startTime = Date.now();
            const checkInterval = setInterval(() => {
                const challengeFrame = document.querySelector('iframe[src*="challenges.cloudflare.com"]');
                if (!challengeFrame) {
                    clearInterval(checkInterval);
                    console.log(`Cloudflare验证完成, 耗时: ${Date.now() - startTime}ms`);
                    resolve();
                }

                // 检查是否超时
                if (Date.now() - startTime > 30000) {
                    clearInterval(checkInterval);
                    console.log('Cloudflare验证等待超时');
                    resolve();
                }
            }, 50);
        });
    }

    // 仿真输入
    async function simulateTyping(element, text) {
        if (!element) {
            console.error('❌ 输入元素不存在');
            return false;
        }

        try {
            // 获取输入框描述符
            const descriptor = Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value');

            // 清空输入框
            descriptor.set.call(element, '');
            await new Promise(resolve => setTimeout(resolve, 100));

            // 设置新值
            descriptor.set.call(element, text);

            // 触发必要的事件
            const events = ['input', 'change', 'focus', 'blur'];
            events.forEach(eventType => {
                const event = new Event(eventType, { bubbles: true });
                element.dispatchEvent(event);
            });

            // 移除invalid类
            element.classList.remove('text-input-box__invalid');

            // 触发React事件
            const reactEvent = {
                target: element,
                currentTarget: element,
                type: 'change',
                nativeEvent: new Event('change')
            };
            element.dispatchEvent(new CustomEvent('react-change', { detail: reactEvent }));

            console.log(`✅ 成功输入文本: ${text.substring(0, 3)}...`);
            return true;
        } catch (error) {
            console.error('❌ 输入模拟失败:', error);
            return false;
        }
    }

    // 点击按钮并等待页面刷新
    function clickButtonAndWait(button, callback) {
        if (button) {
            console.log('准备点击按钮:', button.textContent);
            button.click();
            console.log('按钮点击完成');

            // 等待页面刷新
            let checkCount = 0;
            const maxChecks = 60; // 最多等待30秒
            const checkInterval = setInterval(() => {
                checkCount++;
                // 检查页面是否发生变化
                if (document.readyState === 'complete' &&
                    (document.querySelector('.loading-spinner') === null ||
                     document.querySelector('.loading-overlay') === null)) {
                    clearInterval(checkInterval);
                    console.log('页面刷新完成');
                    if (callback) callback();
                    return;
                }

                if (checkCount >= maxChecks) {
                    clearInterval(checkInterval);
                    console.log('等待页面刷新超时');
                    if (callback) callback();
                }
            }, 500);
        }
    }

    // 强制点击任何元素，使用多种方法尝试
    function forceClick(element) {
        if (!element) {
            console.error('元素不存在，无法点击');
            return false;
        }

        console.log('尝试强制点击元素:', element.tagName, element.id || element.className || '未命名元素');

        try {
            // 尝试多种点击方法

            // 方法1: 原生点击
            element.click();
            console.log('原生点击完成');

            // 方法2: 使用MouseEvent
            try {
                const mouseEvent = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    buttons: 1
                });
                element.dispatchEvent(mouseEvent);
                console.log('MouseEvent点击完成');
            } catch (e) {
                console.log('MouseEvent点击失败:', e);
            }

            // 方法3: 使用HTMLElement的click方法
            try {
                HTMLElement.prototype.click.call(element);
                console.log('HTMLElement点击完成');
            } catch (e) {
                console.log('HTMLElement点击失败:', e);
            }

            // 方法4: jQuery点击（如果可用）
            try {
                if (typeof jQuery !== 'undefined') {
                    jQuery(element).trigger('click');
                    console.log('jQuery点击完成');
                }
            } catch (e) {
                console.log('jQuery点击失败:', e);
            }

            // 方法5: 点击父元素
            try {
                if (element.parentElement) {
                    element.parentElement.click();
                    console.log('父元素点击完成');
                }
            } catch (e) {
                console.log('父元素点击失败:', e);
            }

            return true;
        } catch (error) {
            console.error('强制点击失败:', error);
            return false;
        }
    }

    // 重置部分步骤的执行状态，允许再次执行
    function resetSteps() {
        executedSteps.registration = false;
        executedSteps.verification = false;
        executedSteps.taxpayerInfo = false;
        executedSteps.addressInfo = false;
        executedSteps.consent = false;
        executedSteps.w2Form = false;
        executedSteps.welcomePage = false;
        executedSteps.packageSelection = false;
        executedSteps.upload1040 = false;
        executedSteps.additionalInfo = false;
        executedSteps.filingStatusPage = false;
        executedSteps.dependentsPage = false;
        executedSteps.identityPinPage = false;
        executedSteps.basicInfoSummary = false;
        executedSteps.startPage = false;
        executedSteps.quickFilePage = false;
        executedSteps.readyPage = false;
        executedSteps.w2WageStatementPage = false;
        executedSteps.anythingElsePage = false;
        executedSteps.healthCarePage = false;
        executedSteps.stateReturnPage = false;
        executedSteps.marylandHealthCarePage = false;
        executedSteps.marylandDependentHealthCarePage = false;
        executedSteps.marylandCongratsPage = false;
        executedSteps.stateReturnGridPage = false;
        executedSteps.downloadReturnPage = false;
        executedSteps.readyToFilePage = false;
        executedSteps.efileHubStep1Page = false;
        executedSteps.federalRefundMethodPage = false;
        executedSteps.efileHubStep2Page = false;
        executedSteps.stateRefundMethodPage = false;
        executedSteps.marylandConsentPage = false;
        executedSteps.efileHubStep3Page = false;
        executedSteps.lastLookPage = false;
        executedSteps.protectionPlusPage = false;
        executedSteps.securelyIDPage = false;
        executedSteps.efileHubStep4Page = false;
        executedSteps.bankAccountsPage = false;
        executedSteps.mdDirectDepositPage = false;
        executedSteps.idInfoPage = false;
        executedSteps.agiaAccessPage = false;
        executedSteps.submitPage = false;
        executedSteps.lastStepPage = false;
        executedSteps.pinPage = false;
        executedSteps.bankAccountInfo = false;
        executedSteps.marylandCountyPage = false;
    }

    // 添加执行标记
    const executedSteps = {
        registration: false,
        verification: false,
        taxpayerInfo: false,
        addressInfo: false,
        consent: false,
        w2Form: false,
        welcomePage: false,
        packageSelection: false,
        upload1040: false,
        additionalInfo: false,
        filingStatusPage: false,
        dependentsPage: false,
        identityPinPage: false,
        basicInfoSummary: false,
        startPage: false,
        quickFilePage: false,
        readyPage: false,
        w2WageStatementPage: false,
        anythingElsePage: false,
        healthCarePage: false,
        stateReturnPage: false,
        marylandHealthCarePage: false,
        marylandDependentHealthCarePage: false,
        marylandCongratsPage: false,
        stateReturnGridPage: false,
        downloadReturnPage: false,
        readyToFilePage: false,
        efileHubStep1Page: false,
        federalRefundMethodPage: false,
        efileHubStep2Page: false,
        stateRefundMethodPage: false,
        marylandConsentPage: false,
        efileHubStep3Page: false,
        lastLookPage: false,
        protectionPlusPage: false,
        securelyIDPage: false,
        efileHubStep4Page: false,
        bankAccountsPage: false,
        mdDirectDepositPage: false,
        idInfoPage: false,
        agiAccessPage: false,
        submitPage: false,
        lastStepPage: false,
        pinPage: false,
        bankAccountInfo: false,
        marylandCountyPage: false
    };

    // 步骤6：处理纳税人信息页面
    function step6HandleTaxpayerInfo() {
        console.log('开始处理纳税人信息页面...');
        waitForElement('div.col-md-12 h3', function(titleElement) {
            if (titleElement.textContent === 'Taxpayer\'s Information') {
                console.log('确认是纳税人信息页面');
                const savedData = getData();
                if (savedData.length === 0) {
                    console.log('未找到用户数据');
                    return;
                }
                const data = savedData[savedData.length - 1];

                // 等待页面完全加载
                setTimeout(() => {
                    // 使用ID选择器
                    const firstNameInput = document.querySelector('#info\\.firstName');
                    const lastNameInput = document.querySelector('#info\\.lastName');
                    const ssnInput = document.querySelector('#info\\.socialSecurityNumber');
                    const dobInput = document.querySelector('#info\\.dateOfBirth');

                    if (firstNameInput && lastNameInput && ssnInput && dobInput) {
                        console.log('找到所有输入框');

                        // 按顺序填写
                        simulateTyping(firstNameInput, data.firstName).then(() => {
                            console.log('名字填写完成');
                            simulateTyping(lastNameInput, data.lastName).then(() => {
                                console.log('姓氏填写完成');
                                simulateTyping(ssnInput, data.ssn).then(() => {
                                    console.log('SSN填写完成');
                                    simulateTyping(dobInput, data.dob).then(() => {
                                        console.log('出生日期填写完成');

                                        // 点击出生日期标签
                                        const dobLabel = document.querySelector('label[for="info.dateOfBirth"]');
                                        if (dobLabel) {
                                            console.log('点击出生日期标签');
                                            dobLabel.click();
                                        }

                                        // 等待1秒后点击提交按钮
                                        setTimeout(() => {
                                            const submitButton = document.querySelector('#btnSubmit');
                                            if (submitButton) {
                                                clickButtonAndWait(submitButton, () => {
                                                    console.log('纳税人信息页面提交完成');
                                                });
                                            }
                                        }, 1000);
                                    });
                                });
                            });
                        });
                    } else {
                        console.log('未找到所有输入框，重新尝试...');
                        step6HandleTaxpayerInfo();
                    }
                }, 2000);
            }
        });
    }

    // 步骤7：处理地址信息页面
    function step7HandleAddressInfo() {
        console.log('开始处理地址信息页面...');
        waitForElement('label[for="address.line1"]', function(addressLabel) {
            if (addressLabel.textContent.includes('Address (street number & name)')) {
                console.log('确认是地址信息页面');
                const savedData = getData();
                if (savedData.length === 0) {
                    console.log('未找到用户数据');
                    return;
                }
                const data = savedData[savedData.length - 1];

                // 等待页面完全加载
                setTimeout(() => {
                    waitForElement('#address\\.line1', function(addressInput) {
                        console.log('找到地址输入框');
                        simulateTyping(addressInput, data.address);

                        // 等待地址输入完成
                        setTimeout(() => {
                            waitForElement('#address\\.zip', function(zipInput) {
                                console.log('找到邮编输入框');
                                simulateTyping(zipInput, data.zip).then(() => {
                                    console.log('邮编填写完成');

                                    // 创建一个函数来检查城市和州是否已加载
                                    function checkCityAndState(attempts = 0) {
                                        const cityInput = document.querySelector('#address\\.city');
                                        const stateSelect = document.querySelector('#address\\.state');

                                        // 如果尝试次数超过20次（10秒），就继续下一步
                                        if (attempts >= 20) {
                                            console.log('城市和州信息加载超时，继续下一步');
                                            proceedToNext();
                                            return;
                                        }

                                        // 检查城市和州是否已加载
                                        if (!cityInput || !stateSelect ||
                                            !cityInput.value || cityInput.value.trim() === '' ||
                                            !stateSelect.value || stateSelect.value === 'Select') {

                                            // 触发邮编验证
                                            zipInput.focus();
                                            zipInput.blur();
                                            addressInput.focus();
                                            zipInput.focus();

                                            // 500ms后重试
                                            console.log(`等待城市和州信息加载...尝试第 ${attempts + 1} 次`);
                                            setTimeout(() => checkCityAndState(attempts + 1), 500);
                                            return;
                                        }

                                        console.log('城市和州信息已加载完成');
                                        console.log('城市:', cityInput.value);
                                        console.log('州:', stateSelect.value);

                                        // 城市和州加载完成，继续下一步
                                        proceedToNext();
                                    }

                                    // 处理下一步的函数
                                    function proceedToNext() {
                                        waitForElement('label[for="residentStatus_2Radio"]', function(residentLabel) {
                                            console.log('找到居民状态选项');
                                            if (residentLabel.textContent === 'I am a full-year resident of this state') {
                                                const radioInput = document.querySelector('#residentStatus_2Radio');
                                                if (radioInput) {
                                                    console.log('准备选择居民状态');
                                                    radioInput.click();
                                                    console.log('居民状态选择完成');

                                                    // 等待选择完成后点击提交按钮
                                                    setTimeout(() => {
                                                        waitForElement('#btnSubmit', function(submitButton) {
                                                            if (submitButton) {
                                                                clickButtonAndWait(submitButton, () => {
                                                                    console.log('地址信息页面提交完成');
                                                                });
                                                            }
                                                        });
                                                    }, 1000);
                                                }
                                            }
                                        });
                                    }

                                    // 开始检查城市和州信息
                                    setTimeout(() => checkCityAndState(), 500);
                                });
                            });
                        }, 1000);
                    });
                }, 1000);
            }
        });
    }

    // 步骤13：处理同意页面
    function step13HandleConsent() {
        console.log('开始检查第13步...');
        waitForElement('h1#page-title', function(titleElement) {
            console.log('找到h1标题:', titleElement.textContent);
            if (titleElement.textContent === 'We need your approval on a couple things') {
                console.log('同意页面标题匹配成功');

                // 获取用户数据
                const savedData = getData();
                if (savedData.length === 0) {
                    console.log('未找到用户数据');
                    return;
                }
                const data = savedData[savedData.length - 1];
                const fullName = `${data.firstName} ${data.lastName}`;
                console.log('准备填写的完整姓名:', fullName);

                // 处理第一个同意选项和签名
                function handleFirstConsent() {
                    console.log('开始处理第一个同意选项');
                    waitForElement('label[for="consentToUse_UserConsent"]', function(useConsentLabel) {
                        console.log('找到第一个同意选项');
                        const useConsentCheckbox = document.querySelector('#consentToUse_UserConsent');
                        if (useConsentCheckbox) {
                            console.log('准备点击第一个同意复选框');
                            useConsentCheckbox.click();
                            console.log('第一个同意复选框点击完成');

                            // 等待1秒后填写第一个签名
                            setTimeout(() => {
                                console.log('准备填写第一个签名');
                                const useSignatureInput = document.querySelector('input[name="consentToUse_TpSignature"]');
                                if (useSignatureInput) {
                                    simulateTyping(useSignatureInput, fullName).then(() => {
                                        console.log('第一个签名填写完成');
                                        // 第一个签名完成后,开始处理第二个同意选项
                                        setTimeout(handleSecondConsent, 1000);
                                    });
                                }
                            }, 100);
                        }
                    });
                }

                // 处理第二个同意选项和签名
                function handleSecondConsent() {
                    console.log('开始处理第二个同意选项');
                    waitForElement('label[for="consentToDisclosure_UserConsent"]', function(disclosureConsentLabel) {
                        console.log('找到第二个同意选项');
                        const disclosureConsentCheckbox = document.querySelector('#consentToDisclosure_UserConsent');
                        if (disclosureConsentCheckbox) {
                            console.log('准备点击第二个同意复选框');
                            disclosureConsentCheckbox.click();
                            console.log('第二个同意复选框点击完成');

                            // 等待1秒后填写第二个签名
                            setTimeout(() => {
                                console.log('准备填写第二个签名');
                                const disclosureSignatureInput = document.querySelector('input[name="consentToDisclosure_TpSignature"]');
                                if (disclosureSignatureInput) {
                                    simulateTyping(disclosureSignatureInput, fullName).then(() => {
                                        console.log('第二个签名填写完成');
                                        // 第二个签名完成后,等待1秒点击Continue按钮
                                        setTimeout(handleContinueButton, 1000);
                                    });
                                }
                            }, 100);
                        }
                    });
                }

                // 处理Continue按钮点击
                function handleContinueButton() {
                    console.log('开始处理Continue按钮');
                    waitForElement('#btnSubmit', function(submitButton) {
                        if (submitButton.textContent === 'Continue') {
                            clickButtonAndWait(submitButton, () => {
                                console.log('同意页面提交完成');
                            });
                        } else {
                            console.log('Continue按钮文本不匹配');
                        }
                    });
                }

                // 开始执行第一个同意选项
                handleFirstConsent();
            } else {
                console.log('页面标题不匹配');
            }
        });
    }

    // 添加Zippopotam.us API调用函数
    function getStateByZip(zipCode) {
        return new Promise((resolve, reject) => {
            console.log('开始获取邮编对应的州信息:', zipCode);
            GM_xmlhttpRequest({
                method: 'GET',
                url: `https://api.zippopotam.us/us/${zipCode}`,
                onload: function(response) {
                    try {
                        const data = JSON.parse(response.responseText);
                        const state = data.places[0].state;
                        console.log('获取到州信息:', state);
                        resolve(state);
                    } catch (error) {
                        console.error('解析州信息失败:', error);
                        reject(error);
                    }
                },
                onerror: function(error) {
                    console.error('获取州信息失败:', error);
                    reject(error);
                }
            });
        });
    }

    // 修改州选择逻辑
    async function selectStateByZip(zipCode) {
        try {
            console.log('开始选择州...');
            const state = await getStateByZip(zipCode);

            // 点击州选择按钮
            const stateButton = document.querySelector('#states\\[0\\]\\.state-button');
            if (stateButton) {
                console.log('找到州选择按钮');
                stateButton.click();

                // 等待下拉列表出现
                setTimeout(() => {
                    const dropdownList = document.querySelector('#dropdown-list');
                    if (dropdownList) {
                        console.log('找到州下拉列表');
                        const items = dropdownList.querySelectorAll('li[role="option"]');
                        for (const item of items) {
                            if (item.textContent.trim() === state) {
                                console.log('找到匹配的州:', state);
                                item.click();
                                console.log('已选择州:', state);

                                // 等待2秒后点击Continue按钮
                                setTimeout(() => {
                                    const continueButton = document.querySelector('#btnSubmit');
                                    if (continueButton) {
                                        clickButtonAndWait(continueButton, () => {
                                            console.log('州选择页面提交完成');
                                        });
                                    }
                                }, 2000);
                                break;
                            }
                        }
                    } else {
                        console.log('未找到州下拉列表');
                    }
                }, 1000);
            } else {
                console.log('未找到州选择按钮');
            }
        } catch (error) {
            console.error('选择州失败，使用回退方法:', error);
            fallbackStateSelection(zipCode);
        }
    }

    // 回退方法
    function fallbackStateSelection(zipCode) {
        console.log('使用回退方法选择州');
        const zipFirstDigit = zipCode.charAt(0);
        let targetState = '';

        switch(zipFirstDigit) {
            case '0':
            case '1':
                targetState = 'Massachusetts';
                break;
            case '2':
                targetState = 'New York';
                break;
            case '3':
                targetState = 'New Jersey';
                break;
            case '4':
                targetState = 'Pennsylvania';
                break;
            case '5':
                targetState = 'Iowa';
                break;
            case '6':
                targetState = 'Illinois';
                break;
            case '7':
                targetState = 'Louisiana';
                break;
            case '8':
                targetState = 'Colorado';
                break;
            case '9':
                targetState = 'California';
                break;
        }

        console.log('根据邮编第一位数字选择州:', targetState);

        // 使用原来的选择逻辑
        const stateButton = document.querySelector('#states\\[0\\]\\.state-button');
        if (stateButton) {
            stateButton.click();
            setTimeout(() => {
                const dropdownList = document.querySelector('#dropdown-list');
                if (dropdownList) {
                    const items = dropdownList.querySelectorAll('li[role="option"]');
                    for (const item of items) {
                        if (item.textContent.trim() === targetState) {
                            item.click();
                            console.log('已选择州:', targetState);

                            // 等待2秒后点击Continue按钮
                            setTimeout(() => {
                                const continueButton = document.querySelector('#btnSubmit');
                                if (continueButton) {
                                    clickButtonAndWait(continueButton, () => {
                                        console.log('州选择页面提交完成');
                                    });
                                }
                            }, 2000);
                            break;
                        }
                    }
                }
            }, 1000);
        }
    }

    // 步骤18：处理W-2表单填写
    function step27HandleW2Form() {
        console.log('开始处理W-2表单...');
        waitForElement('h1[data-testid="page-title-h1"]', function(titleElement) {
            if (titleElement.textContent === 'Wage and tax statement') {
                console.log('确认是W-2表单页面');
                const savedData = getData();
                if (savedData.length === 0) {
                    console.log('未找到用户数据');
                    return;
                }
                const data = savedData[savedData.length - 1];

                // 等待页面完全加载
                setTimeout(() => {
                    // 填写雇主EIN
                    const einInput = document.querySelector('#employer\\.ein');
                    if (einInput) {
                        console.log('找到EIN输入框');
                        simulateTyping(einInput, data.employerEin);
                    }

                    // 填写雇主公司名称
                    const employerNameInput = document.querySelector('#employer\\.name');
                    if (employerNameInput) {
                        console.log('找到雇主公司名称输入框');
                        simulateTyping(employerNameInput, data.employerName);
                    }

                    // 填写雇主地址
                    const employerAddressInput = document.querySelector('#employer\\.address\\.street');
                    if (employerAddressInput) {
                        console.log('找到雇主地址输入框');
                        simulateTyping(employerAddressInput, data.employerAddress);
                    }

                    // 填写雇主邮编
                    const employerZipInput = document.querySelector('#employer\\.address\\.zip');
                    if (employerZipInput) {
                        console.log('找到雇主邮编输入框');
                        simulateTyping(employerZipInput, data.employerZip).then(() => {
                            console.log('雇主邮编填写完成');

                            // 创建一个函数来检查雇主城市和州是否已加载
                            function checkEmployerCityAndState(attempts = 0) {
                                const cityInput = document.querySelector('#employer\\.address\\.city');
                                const stateSelect = document.querySelector('#employer\\.address\\.state');

                                // 如果尝试次数超过20次（10秒），就继续下一步
                                if (attempts >= 3) {
                                    console.log('雇主城市和州信息加载超时，继续下一步');
                                    proceedToNext();
                                    return;
                                }

                                // 检查城市和州是否已加载
                                if (!cityInput || !stateSelect ||
                                    !cityInput.value || cityInput.value.trim() === '' ||
                                    !stateSelect.value || stateSelect.value === 'Select') {

                                    // 触发邮编验证
                                    employerZipInput.focus();
                                    employerZipInput.blur();
                                    employerAddressInput.focus();
                                    employerZipInput.focus();

                                    // 500ms后重试
                                    console.log(`等待雇主城市和州信息加载...尝试第 ${attempts + 1} 次`);
                                    setTimeout(() => checkEmployerCityAndState(attempts + 1), 500);
                                    return;
                                }

                                console.log('雇主城市和州信息已加载完成');
                                console.log('雇主城市:', cityInput.value);
                                console.log('雇主州:', stateSelect.value);

                                // 城市和州加载完成，继续下一步
                                proceedToNext();
                            }

                            // 处理下一步的函数
                            function proceedToNext() {
                                // 填写州EIN
                                const stateEinInput = document.querySelector('#states\\[0\\]\\.ein');
                                if (stateEinInput) {
                                    console.log('找到州EIN输入框');
                                    simulateTyping(stateEinInput, data.employerEin).then(() => {
                                        console.log('州EIN填写完成');

                                        // 等待2秒后选择州,使用导入资料中的邮编
                                        setTimeout(() => {
                                            selectStateByZip(data.zip);
                                        }, 2000);
                                    });
                                }

                                // 填写金额1-6
                                const federalWagesInput = document.querySelector('#information\\.federalWages');
                                if (federalWagesInput) {
                                    console.log('找到金额1输入框');
                                    simulateTyping(federalWagesInput, data.amount1);
                                }

                                const federalWithheldInput = document.querySelector('#information\\.federalWithheld');
                                if (federalWithheldInput) {
                                    console.log('找到金额2输入框');
                                    simulateTyping(federalWithheldInput, data.amount2);
                                }

                                const socialSecurityWagesInput = document.querySelector('#information\\.socialSecurityWages');
                                if (socialSecurityWagesInput) {
                                    console.log('找到金额3输入框');
                                    simulateTyping(socialSecurityWagesInput, data.amount3);
                                }

                                const socialSecurityWithheldInput = document.querySelector('#information\\.socialSecurityWithheld');
                                if (socialSecurityWithheldInput) {
                                    console.log('找到金额4输入框');
                                    simulateTyping(socialSecurityWithheldInput, data.amount4);
                                }

                                const medicareWagesInput = document.querySelector('#information\\.medicareWages');
                                if (medicareWagesInput) {
                                    console.log('找到金额5输入框');
                                    simulateTyping(medicareWagesInput, data.amount5);
                                }

                                const medicareWithheldInput = document.querySelector('#information\\.medicareWithheld');
                                if (medicareWithheldInput) {
                                    console.log('找到金额6输入框');
                                    simulateTyping(medicareWithheldInput, data.amount6);
                                }

                                // 填写金额16和17
                                const amount16Input = document.querySelector('#states\\[0\\]\\.wage');
                                if (amount16Input) {
                                    console.log('找到金额16输入框');
                                    simulateTyping(amount16Input, data.amount16);
                                }

                                const amount17Input = document.querySelector('#states\\[0\\]\\.tax');
                                if (amount17Input) {
                                    console.log('找到金额17输入框');
                                    simulateTyping(amount17Input, data.amount17);
                                }

                                // 等待2秒后点击Continue按钮
                                setTimeout(() => {
                                    const continueButton = document.querySelector('#btnSubmit');
                                    if (continueButton) {
                                        clickButtonAndWait(continueButton, () => {
                                            console.log('W-2表单页面提交完成');
                                        });
                                    }
                                }, 2000);
                            }

                            // 开始检查雇主城市和州信息
                            setTimeout(() => checkEmployerCityAndState(), 500);
                        });
                    }
                }, 2000);
            }
        });
    }

    // 步骤8：处理附加信息页面
    function step8HandleAdditionalInfo() {
        console.log('开始处理附加信息页面...');
        waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
            const pageTitle = document.querySelector('h1[data-testid="page-title-h1"]');
            if (pageTitle && pageTitle.textContent === 'Additional Information') {
                console.log('确认是附加信息页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"Continue"按钮
                    const continueButton = document.querySelector('button#btnSubmit[title="Continue"]');
                    if (continueButton) {
                        console.log('找到Continue按钮');
                        clickButtonAndWait(continueButton, () => {
                            console.log('附加信息页面提交完成');
                        });
                    } else {
                        console.log('未找到Continue按钮');
                    }
                }, 1000);
            }
        });
    }

    // 修改步骤9：处理报税状态页面
    function step9HandleFilingStatusPage() {
        console.log('=== 开始处理报税状态页面 ===');

        // 防止重复执行
        if (executedSteps.filingStatusPage) {
            console.log('报税状态页面已处理过，跳过...');
            return;
        }

        try {
            // 检查页面元素
            const filingWizardDiv = document.querySelector('div#filingWizard');
            if (!filingWizardDiv) {
                console.log('未找到filingWizard div');
                return;
            }

            // 检查文字内容
            const strongText = filingWizardDiv.querySelector('strong');
            if (!strongText || strongText.textContent !== 'Need help determining your filing status?') {
                console.log('未找到匹配的文字内容');
                return;
            }

            console.log('✅ 确认是报税状态页面');

            // 等待1秒后执行点击操作
            setTimeout(() => {
                // 查找并点击Single选项
                const singleLabel = document.querySelector('label[for="filingStatus_1Radio"]');
                const singleRadio = document.getElementById('filingStatus_1Radio');

                if (singleLabel && singleRadio) {
                    console.log('找到Single选项');

                    // 点击label
                    singleLabel.click();
                    console.log('Single label已点击');

                    // 设置radio状态
                    singleRadio.checked = true;
                    singleRadio.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('Radio按钮状态已设置');

                    // 等待500ms后点击Continue按钮
                    setTimeout(() => {
                        const continueBtn = document.querySelector('button#btnSubmit[title="Continue"]');
                        if (continueBtn) {
                            console.log('找到Continue按钮');

                            // 移除禁用状态
                            continueBtn.disabled = false;
                            continueBtn.classList.remove('btn-not-ready');

                            // 使用多种方式点击按钮
                            try {
                                // 方法1：直接点击
                                continueBtn.click();
                                console.log('直接点击完成');

                                // 方法2：使用MouseEvent
                                const clickEvent = new MouseEvent('click', {
                                    view: window,
                                    bubbles: true,
                                    cancelable: true
                                });
                                continueBtn.dispatchEvent(clickEvent);
                                console.log('MouseEvent点击完成');

                                // 方法3：使用forceClick
                                forceClick(continueBtn);
                                console.log('forceClick完成');

                                // 方法4：如果是submit类型按钮，尝试提交父表单
                                if (continueBtn.type === 'submit') {
                                    const form = continueBtn.closest('form');
                                    if (form) {
                                        console.log('尝试提交父表单');
                                        form.submit();
                                    }
                                }

                                console.log('Continue按钮点击完成');
                                executedSteps.filingStatusPage = true;
                            } catch (error) {
                                console.error('点击Continue按钮时出错:', error);
                            }
                        } else {
                            console.log('未找到Continue按钮');
                        }
                    }, 500);
                } else {
                    console.log('未找到Single选项');
                }
            }, 1000);

        } catch (error) {
            console.error('处理报税状态页面时出错:', error);
        }
    }

    // 步骤10：处理受抚养人页面
    function step10HandleDependents() {
        console.log('开始处理受抚养人页面...');
        waitForElement('div#content-header.content-header', function(divElement) {
            const pageTitle = document.querySelector('h1#page-title');
            if (pageTitle && pageTitle.textContent === 'Dependents or Qualifying Person(s)') {
                console.log('确认是受抚养人页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"No"选项
                    const noLabel = document.querySelector('label[for="needToAddDependent_2Radio"]');
                    if (noLabel) {
                        console.log('找到No选项');
                        noLabel.click();
                        console.log('No选项已选择');

                        // 等待1秒后点击Continue按钮
                        setTimeout(() => {
                            const continueButton = document.querySelector('button#btnSubmit[title="Continue"]');
                            if (continueButton) {
                                clickButtonAndWait(continueButton, () => {
                                    console.log('受抚养人页面提交完成');
                                });
                            } else {
                                console.log('未找到Continue按钮');
                            }
                        }, 1000);
                    } else {
                        console.log('未找到No选项');
                    }
                }, 1000);
            }
        });
    }

    // 步骤11：处理身份保护PIN页面
    function step11HandleIdentityPin() {
        console.log('开始处理身份保护PIN页面...');
        waitForElement('div#content-header.content-header', function(divElement) {
            const pageTitle = document.querySelector('h1#page-title');
            if (pageTitle && pageTitle.textContent === 'IRS Identity Protection PIN') {
                console.log('确认是身份保护PIN页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"No"选项
                    const noLabel = document.querySelector('label[for="selection_falseRadio"]');
                    if (noLabel) {
                        console.log('找到No选项');
                        noLabel.click();
                        console.log('No选项已选择');

                        // 等待1秒后点击Continue按钮
                        setTimeout(() => {
                            const continueButton = document.querySelector('button#btnSubmit[title="Continue"]');
                            if (continueButton) {
                                clickButtonAndWait(continueButton, () => {
                                    console.log('身份保护PIN页面提交完成');
                                });
                            } else {
                                console.log('未找到Continue按钮');
                            }
                        }, 1000);
                    } else {
                        console.log('未找到No选项');
                    }
                }, 1000);
            }
        });
    }

    // 步骤12：处理基本信息摘要页面
    function step12HandleBasicInfoSummary() {
        console.log('开始处理基本信息摘要页面...');
        waitForElement('div#page-header', function(divElement) {
            const pageTitle = document.querySelector('h1#page-title');
            if (pageTitle && pageTitle.textContent === 'Basic Information Summary') {
                console.log('确认是基本信息摘要页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"Continue"链接
                    const continueLink = document.querySelector('a#btn-Save-BasicInfoSummary');
                    if (continueLink) {
                        console.log('找到Continue链接');
                        clickButtonAndWait(continueLink, () => {
                            console.log('基本信息摘要页面提交完成');
                        });
                    } else {
                        console.log('未找到Continue链接');
                    }
                }, 1000);
            }
        });
    }

    // 步骤14：处理开始页面
    function step14HandleStartPage() {
        console.log('开始处理开始页面...');
        waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
            const pageTitle = document.querySelector('h1[data-testid="page-title-h1"]');
            if (pageTitle && pageTitle.textContent === 'Let\'s do this! #slayit') {
                console.log('确认是开始页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"Continue"按钮
                    const continueButton = document.querySelector('button#registration-continue-submit');
                    if (continueButton) {
                        console.log('找到Continue按钮');
                        clickButtonAndWait(continueButton, () => {
                            console.log('开始页面提交完成');
                        });
                    } else {
                        console.log('未找到Continue按钮');
                    }
                }, 1000);
            }
        });
    }

    // 步骤15：处理快速文件页面
    function step15HandleQuickFilePage() {
        console.log('开始处理快速文件页面...');
        waitForElement('div#formsInHand-chooseWrapper', function(divElement) {
            if (divElement) {
                console.log('确认是快速文件页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"Continue"按钮
                    const continueButton = document.querySelector('button#btnSubmit[title="Continue"]');
                    if (continueButton) {
                        console.log('找到Continue按钮');
                        clickButtonAndWait(continueButton, () => {
                            console.log('快速文件页面提交完成');
                        });
                    } else {
                        console.log('未找到Continue按钮');
                    }
                }, 1000);
            }
        });
    }

    // 步骤16：处理导航栏页面
    function step16HandleNavBarPage() {
        console.log('开始处理导航栏页面...');
        waitForElement('nav.navbar', function(navElement) {
            const continueLink = navElement.querySelector('a.btn.btn-success');
            if (continueLink && continueLink.textContent.includes('Continue')) {
                console.log('确认是导航栏页面');
                console.log('找到Continue链接');
                clickButtonAndWait(continueLink, function() {
                    console.log('导航栏页面提交完成');
                });
            }
        });
    }

    // 步骤17：处理准备页面
    function step17HandleReadyPage() {
        console.log('开始处理准备页面...');
        waitForElement('div#formsInHand-readyWrapper', function(divElement) {
            if (divElement) {
                console.log('确认是准备页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"Continue"按钮
                    const continueButton = document.querySelector('button#add-state-btn');
                    if (continueButton) {
                        console.log('找到Continue按钮');
                        clickButtonAndWait(continueButton, () => {
                            console.log('准备页面提交完成');
                        });
                    } else {
                        console.log('未找到Continue按钮');
                    }
                }, 5000); // 等待4-5秒
            }
        });
    }

    // 步骤18：处理W2表单选择页面
    function step18HandleW2FormSelectPage() {
        console.log('开始处理W2表单选择页面...');
        waitForElement('button.add-w2', function(addW2Button) {
            console.log('确认是W2表单选择页面');
            console.log('找到添加W2按钮');
            clickButtonAndWait(addW2Button, function() {
                console.log('W2表单选择页面处理完成');
            });
        });
    }

    // 步骤19：处理W-2工资声明页面
    function step19HandleW2WageStatementPage() {
        console.log('开始处理W-2工资声明页面...');
        waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
            const pageTitle = document.querySelector('h1[data-testid="page-title-h1"]');
            if (pageTitle && pageTitle.textContent === 'W-2 Wage Statement') {
                console.log('确认是W-2工资声明页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"Continue"按钮
                    const continueButton = document.querySelector('button#btnSubmit[title="Continue"]');
                    if (continueButton) {
                        console.log('找到Continue按钮');
                        clickButtonAndWait(continueButton, () => {
                            console.log('W-2工资声明页面提交完成');
                        });
                    } else {
                        console.log('未找到Continue按钮');
                    }
                }, 1000);
            }
        });
    }

    // 步骤20：处理其他事项页面
    function step20HandleAnythingElsePage() {
        console.log('开始处理其他事项页面...');
        waitForElement('div#content-header.content-header', function(divElement) {
            const pageTitle = document.querySelector('h1[data-testid="page-title-h1"]');
            if (pageTitle && pageTitle.textContent === 'Anything else?') {
                console.log('确认是其他事项页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"Continue"按钮
                    const continueButton = document.querySelector('button#btnSubmit[title="Continue"]');
                    if (continueButton) {
                        console.log('找到Continue按钮');
                        clickButtonAndWait(continueButton, () => {
                            console.log('其他事项页面提交完成');
                        });
                    } else {
                        console.log('未找到Continue按钮');
                    }
                }, 1000);
            }
        });
    }

    // 步骤21：处理平价医疗法案页面
    function step21HandleHealthCarePage() {
        console.log('开始处理平价医疗法案页面...');
        waitForElement('div#content-header.content-header', function(divElement) {
            const pageTitle = document.querySelector('h1[data-testid="page-title"]');
            if (pageTitle && pageTitle.textContent === 'Affordable Care Act Health Insurance') {
                console.log('确认是平价医疗法案页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"No"选项
                    const noLabel = document.querySelector('label[for="marketPlace_falseRadio"]');
                    if (noLabel) {
                        console.log('找到No选项');
                        noLabel.click();
                        console.log('No选项已选择');

                        // 等待1秒后点击Continue按钮
                        setTimeout(() => {
                            const continueButton = document.querySelector('button#btnSubmit[title="Continue"]');
                            if (continueButton) {
                                clickButtonAndWait(continueButton, () => {
                                    console.log('平价医疗法案页面提交完成');
                                });
                            } else {
                                console.log('未找到Continue按钮');
                            }
                        }, 1000);
                    } else {
                        console.log('未找到No选项');
                    }
                }, 1000);
            }
        });
    }

    // 步骤22：处理州申报开始页面
    function step22HandleStateReturnPage() {
        console.log('开始处理州申报开始页面...');
        waitForElement('div#content-header.content-header', function(divElement) {
            const pageTitle = document.querySelector('h1#page-title');
            if (pageTitle && pageTitle.textContent === 'Let\'s get started on your state return') {
                console.log('确认是州申报开始页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"Get Started"链接
                    const getStartedLink = document.querySelector('a#add-state-return');
                    if (getStartedLink) {
                        console.log('找到Get Started链接');
                        clickButtonAndWait(getStartedLink, () => {
                            console.log('州申报开始页面提交完成');
                        });
                    } else {
                        console.log('未找到Get Started链接');
                    }
                }, 1000);
            }
        });
    }

    // 步骤23：处理马里兰州健康保险页面
    function step23HandleMarylandHealthCarePage() {
        console.log('开始处理马里兰州健康保险页面...');
        waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
            const stateName = document.querySelector('h1');
            if (stateName && stateName.textContent === 'Maryland Return') {
                const question = document.querySelector('h3.subsection');
                if (question && question.textContent === 'Does the taxpayer have health care coverage?') {
                    console.log('确认是马里兰州健康保险页面');
                    // 等待页面加载
                    setTimeout(() => {
                        // 点击"No"选项
                        const noButton = document.querySelectorAll('span.btn.btn-lg.btn-primary.btn.btn-lg.save.btn-continue')[1];
                        if (noButton) {
                            console.log('找到No按钮');
                            clickButtonAndWait(noButton, () => {
                                console.log('马里兰州健康保险页面提交完成');
                            });
                        } else {
                            console.log('未找到No按钮');
                        }
                    }, 1000);
                }
            }
        });
    }

    // 步骤24：处理马里兰州受抚养人健康保险页面
    function step24HandleMarylandDependentHealthCarePage() {
        console.log('开始处理马里兰州受抚养人健康保险页面...');
        waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
            const stateName = document.querySelector('h1');
            if (stateName && stateName.textContent === 'Maryland Return') {
                const question = document.querySelector('h3.subsection');
                if (question && question.textContent === 'Do all the dependents have health care coverage?') {
                    console.log('确认是马里兰州受抚养人健康保险页面');
                    // 等待页面加载
                    setTimeout(() => {
                        // 点击"No"选项
                        const noButton = document.querySelectorAll('span.btn.btn-lg.btn-primary.btn.btn-lg.save.btn-continue')[1];
                        if (noButton) {
                            console.log('找到No按钮');
                            clickButtonAndWait(noButton, () => {
                                console.log('马里兰州受抚养人健康保险页面提交完成');
                            });
                        } else {
                            console.log('未找到No按钮');
                        }
                    }, 1000);
                }
            }
        });
    }

    // 步骤25：处理马里兰州祝贺页面
    function step25HandleMarylandCongratsPage() {
        console.log('开始处理马里兰州祝贺页面...');
        waitForElement('div#content-header.content-header', function(divElement) {
            const pageTitle = document.querySelector('h1#page-title');
            if (pageTitle && pageTitle.innerHTML.includes('Congrats!')) {
                console.log('确认是马里兰州祝贺页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"No, continue"链接
                    const noContinueLink = document.querySelector('a#btnNo');
                    if (noContinueLink) {
                        console.log('找到No, continue链接');
                        clickButtonAndWait(noContinueLink, () => {
                            console.log('马里兰州祝贺页面提交完成');
                        });
                    } else {
                        console.log('未找到No, continue链接');
                    }
                }, 1000);
            }
        });
    }

    // 步骤26：处理州申报网格页面
    function step26HandleStateReturnGridPage() {
        console.log('开始处理州申报网格页面...');
        waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
            const pageTitle = document.querySelector('h1');
            if (pageTitle && pageTitle.textContent === 'State Return') {
                console.log('确认是州申报网格页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"Continue"按钮
                    const continueButton = document.querySelector('button#btnContinue');
                    if (continueButton) {
                        console.log('找到Continue按钮');
                        clickButtonAndWait(continueButton, () => {
                            console.log('州申报网格页面提交完成');
                        });
                    } else {
                        console.log('未找到Continue按钮');
                    }
                }, 1000);
            }
        });
    }

    // 步骤40：处理下载申报表页面
    function step40HandleDownloadReturnPage() {
        console.log('开始处理下载申报表页面...');

        // 等待页面加载
        setTimeout(() => {
            try {
                console.log('尝试点击Continue按钮...');

                // 方法1: 使用ID和class选择器
                const continueBtn = document.querySelector('button#btnSubmit.btn.btn-lg.btn-continue');
                if (continueBtn) {
                    console.log('找到Continue按钮:', continueBtn.id, continueBtn.className);
                    continueBtn.click();
                    console.log('已点击Continue按钮');
                } else {
                    console.log('未找到ID为btnSubmit的按钮，尝试其他方法...');

                    // 方法2: 使用多种选择器组合
                    const buttonSelectors = [
                        'button#btnSubmit',
                        'button.btn-continue',
                        'button[title="Continue"]',
                        'button.btn-lg[title="Continue"]',
                        'button[type="button"][title="Continue"]'
                    ];

                    let buttonFound = false;
                    for (const selector of buttonSelectors) {
                        const btn = document.querySelector(selector);
                        if (btn) {
                            console.log(`找到按钮，使用选择器: ${selector}`);
                            btn.click();
                            console.log('按钮已点击');
                            buttonFound = true;
                            break;
                        }
                    }

                    if (!buttonFound) {
                        console.log('使用选择器未找到按钮，尝试JavaScript注入...');

                        // 方法3: 使用JavaScript注入
                        const script = `
                            // 查找Continue按钮
                            var continueBtn = document.getElementById('btnSubmit');
                            if(continueBtn) {
                                console.log('找到Continue按钮，点击');
                                continueBtn.click();
                            } else {
                                console.log('未找到ID为btnSubmit的按钮');

                                // 尝试其他选择器
                                var submitBtns = document.querySelectorAll('button[type="submit"]');
                                console.log('找到type=submit的按钮数量: ' + submitBtns.length);

                                if(submitBtns.length > 0) {
                                    console.log('点击第一个提交按钮');
                                    submitBtns[0].click();
                                } else {
                                    console.log('寻找包含Continue文本的按钮');
                                    var allBtns = document.querySelectorAll('button');
                                    var continueFound = false;

                                    allBtns.forEach(function(btn) {
                                        if(btn.textContent.includes('Continue') || btn.title === 'Continue') {
                                            console.log('找到包含Continue的按钮，点击');
                                            btn.click();
                                            continueFound = true;
                                        }
                                    });

                                    if(!continueFound) {
                                        console.log('寻找表单并直接提交');
                                        var form = document.querySelector('form');
                                        if(form) {
                                            console.log('找到表单，提交');
                                            form.submit();
                                        } else {
                                            console.log('未找到任何表单');
                                        }
                                    }
                                }
                            }
                        `;

                        const scriptElement = document.createElement('script');
                        scriptElement.textContent = script;
                        document.body.appendChild(scriptElement);
                        console.log('执行了Continue按钮点击脚本');
                    }
                }

                // 方法4: 使用forceClick函数
                setTimeout(() => {
                    try {
                        console.log('尝试使用forceClick函数...');
                        const btn = document.querySelector('button#btnSubmit');
                        if (btn) {
                            console.log('找到按钮，使用forceClick');
                            forceClick(btn);
                            console.log('已使用forceClick点击按钮');
                        }
                    } catch (e) {
                        console.error('forceClick执行失败:', e);
                    }
                }, 1000);

                // 方法5: 使用clickButtonAndWait函数
                setTimeout(() => {
                    try {
                        console.log('尝试使用clickButtonAndWait函数...');
                        const btn = document.querySelector('button#btnSubmit');
                        if (btn) {
                            console.log('找到按钮，使用clickButtonAndWait');
                            clickButtonAndWait(btn, () => {
                                console.log('按钮点击完成');
                            });
                        }
                    } catch (e) {
                        console.error('clickButtonAndWait执行失败:', e);
                    }
                }, 2000);

            } catch (error) {
                console.error('处理下载申报表页面时出错:', error);
            }
        }, 3000); // 等待3秒让页面完全加载
    }

    // 步骤28：处理准备提交页面
    function step28HandleReadyToFilePage() {
        console.log('开始处理准备提交页面...');
        waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
            const pageTitle = document.querySelector('h1');
            if (pageTitle && pageTitle.textContent === 'Ready to file?') {
                console.log('确认是准备提交页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"Yes"选项
                    const yesLabel = document.querySelector('label[for="efileSelection_1Radio"]');
                    if (yesLabel) {
                        console.log('找到Yes选项');
                        yesLabel.click();
                        console.log('Yes选项已选择');
                    } else {
                        console.log('未找到Yes选项');
                    }
                }, 1000);
            }
        });
    }

    // 修改步骤29：处理E-file中心页面
    function step29HandleEfileHubPage() {
        console.log('=== 开始处理E-file中心页面 ===');

        try {
            // 检查页面标题
            const titleElement = document.querySelector('div.content-columns__header__column-1 div');
            if (!titleElement || titleElement.textContent !== 'Federal') {
                console.log('不是E-file中心页面或标题不匹配');
                return;
            }
            console.log('✅ 确认是E-file中心页面');

            // 等待1秒后执行
            setTimeout(() => {
                // 优先查找Sign and File按钮
                const signAndFileButton = document.querySelector('button#signandfile-button[type="submit"].btn.btn-lg.btn-continue');

                if (signAndFileButton) {
                    console.log('找到Sign and File按钮:', {
                        id: signAndFileButton.id,
                        className: signAndFileButton.className,
                        text: signAndFileButton.textContent.trim()
                    });

                    // 确保按钮在视图中可见
                    signAndFileButton.scrollIntoView({behavior: 'smooth', block: 'center'});

                    // 等待滚动完成后执行点击
                    setTimeout(() => {
                        try {
                            // 方法1：直接点击
                            signAndFileButton.click();
                            console.log('直接点击完成');

                            // 方法2：使用MouseEvent
                            const clickEvent = new MouseEvent('click', {
                                view: window,
                                bubbles: true,
                                cancelable: true
                            });
                            signAndFileButton.dispatchEvent(clickEvent);
                            console.log('MouseEvent点击完成');

                            // 方法3：使用forceClick
                            forceClick(signAndFileButton);
                            console.log('forceClick完成');

                            // 方法4：如果是submit类型按钮，尝试提交父表单
                            if (signAndFileButton.type === 'submit') {
                                const form = signAndFileButton.closest('form');
                                if (form) {
                                    console.log('尝试提交父表单');
                                    form.submit();
                                }
                            }

                            console.log('所有点击方法已尝试');
                            executedSteps.efileHubPage = true;
                        } catch (error) {
                            console.error('点击Sign and File按钮时出错:', error);
                        }
                    }, 1000);
                } else {
                    console.log('❌ 未找到Sign and File按钮');

                    // 如果没找到Sign and File按钮，尝试其他可能的继续元素
                    const otherElements = [
                        document.querySelector('a#state-continue-button.btn.btn-lg.btn-submit[href="/2024/avalon/efile/stateMethod/MD"]'),
                        document.querySelector('a.btn.btn-lg.btn-submit[href="/2024/avalon/efile/federalMethod"]'),
                        document.querySelector('a#signandfile-more-button[href="/2024/avalon/efile/savingsBondQuestion"]'),
                        document.querySelector('a.btn.btn-lg.btn-submit')
                    ];

                    // 找到的第一个有效元素
                    const element = otherElements.find(el => el !== null);

                    if (element) {
                        console.log('找到替代元素:', {
                            id: element.id,
                            className: element.className,
                            type: element.tagName,
                            text: element.textContent.trim(),
                            href: element.href || 'N/A'
                        });

                        // 使用多种方式点击元素
                        element.scrollIntoView({behavior: 'smooth', block: 'center'});

                        setTimeout(() => {
                            forceClick(element);

                            if (element.tagName === 'A' && element.href) {
                                setTimeout(() => {
                                    window.location.href = element.href;
                                }, 500);
                            }
                        }, 1000);
                    } else {
                        console.log('❌ 未找到任何可点击元素');
                    }
                }
            }, 1000);

        } catch (error) {
            console.error('处理E-file中心页面时出错:', error);
        }
    }

    // 步骤30：处理联邦退款方式页面
    function step30HandleFederalRefundMethodPage() {
        console.log('开始处理联邦退款方式页面...');
        waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
            const pageTitle = document.querySelector('h1');
            if (pageTitle && pageTitle.textContent === 'Federal Refund Method') {
                console.log('确认是联邦退款方式页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"Mailed check"选项
                    const mailedCheckDiv = document.querySelector('div#guide-me-boxlabel-1.guideContent__head');
                    if (mailedCheckDiv) {
                        console.log('找到Mailed check选项');
                        mailedCheckDiv.click();

                        // 等待1秒后点击Continue按钮
                        setTimeout(() => {
                            const continueButton = document.querySelector('button#btnSubmit[type="submit"]');
                            if (continueButton) {
                                clickButtonAndWait(continueButton, () => {
                                    console.log('联邦退款方式页面提交完成');
                                });
                            } else {
                                console.log('未找到Continue按钮');
                            }
                        }, 1000);
                    } else {
                        console.log('未找到Mailed check选项');
                    }
                }, 1000);
            }
        });
    }

    // 步骤31：处理E-file中心第2步页面
    function step31HandleEfileHubStep2Page() {
        console.log('开始处理E-file中心第2步页面...');
        waitForElement('div.efilehub-cards__steppers', function(steppersDiv) {
            const stepDiv = steppersDiv.querySelector('div.step');
            if (stepDiv && stepDiv.textContent === '2') {
                console.log('确认是E-file中心第2步页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"Continue"链接
                    const continueLink = document.querySelector('a#state-continue-button');
                    if (continueLink) {
                        console.log('找到Continue链接');
                        clickButtonAndWait(continueLink, () => {
                            console.log('E-file中心第2步页面提交完成');
                        });
                    } else {
                        console.log('未找到Continue链接');
                    }
                }, 1000);
            }
        });
    }

    // 步骤32：处理州退款方式页面
    function step32HandleStateRefundMethodPage() {
        console.log('开始处理州退款方式页面...');
        waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
            const pageTitle = document.querySelector('h1');
            if (pageTitle && pageTitle.textContent === 'State Refund Method') {
                console.log('确认是州退款方式页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"Direct deposit"选项
                    const directDepositLabel = document.querySelector('div#guide-me-boxlabel-4.guideContent__head');
                    if (directDepositLabel) {
                        console.log('找到Direct deposit选项');
                        directDepositLabel.click();

                        // 等待1秒后点击Continue按钮
                        setTimeout(() => {
                            const continueButton = document.querySelector('button#btnSubmit[type="submit"]');
                            if (continueButton && !continueButton.disabled) {
                                clickButtonAndWait(continueButton, () => {
                                    console.log('州退款方式页面提交完成');
                                });
                            } else {
                                console.log('未找到有效的Continue按钮');
                            }
                        }, 1000);
                    } else {
                        console.log('未找到Direct deposit选项');
                    }
                }, 1000);
            }
        });
    }

    // 步骤33：处理马里兰州同意披露页面
    function step33HandleMarylandConsentPage() {
        console.log('开始处理马里兰州同意披露页面...');
        waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
            const pageTitle = document.querySelector('h1');
            if (pageTitle && pageTitle.textContent === 'Maryland - Online Filing Consent to Disclose and Perjury Statement') {
                console.log('确认是马里兰州同意披露页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击同意复选框
                    const consentLabel = document.querySelector('label[for="didTaxpayerAccept"]');
                    if (consentLabel) {
                        console.log('找到同意复选框');
                        consentLabel.click();

                        // 等待1秒后点击Continue链接
                        setTimeout(() => {
                            const continueLink = document.querySelector('a[href="/2024/avalon/efile/efileHub"]');
                            if (continueLink) {
                                clickButtonAndWait(continueLink, () => {
                                    console.log('马里兰州同意披露页面提交完成');
                                });
                            } else {
                                console.log('未找到Continue链接');
                            }
                        }, 1000);
                    } else {
                        console.log('未找到同意复选框');
                    }
                }, 1000);
            }
        });
    }

    // 步骤34：处理E-file中心第3步页面
    function step34HandleEfileHubStep3Page() {
        console.log('开始处理E-file中心第3步页面...');
        waitForElement('div.efilehub-cards__steppers', function(steppersDiv) {
            const stepDiv = steppersDiv.querySelector('div.step');
            if (stepDiv && stepDiv.textContent === '3') {
                console.log('确认是E-file中心第3步页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"Continue"链接
                    const continueLink = document.querySelector('a#productsandservices-continue');
                    if (continueLink) {
                        console.log('找到Continue链接');
                        clickButtonAndWait(continueLink, () => {
                            console.log('E-file中心第3步页面提交完成');
                        });
                    } else {
                        console.log('未找到Continue链接');
                    }
                }, 1000);
            }
        });
    }

    // 步骤35：处理Last Look页面
    function step35HandleLastLookPage() {
        console.log('开始处理Last Look页面...');
        waitForElement('main#lastLookOffer', function(mainElement) {
            if (mainElement) {
                console.log('确认是Last Look页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"No, thanks"按钮
                    const noThanksButton = document.querySelector('button#lastLook-btnSkip');
                    if (noThanksButton) {
                        console.log('找到No, thanks按钮');
                        clickButtonAndWait(noThanksButton, () => {
                            console.log('Last Look页面提交完成');
                        });
                    } else {
                        console.log('未找到No, thanks按钮');
                    }
                }, 1000);
            }
        });
    }

    // 步骤36：处理Protection Plus页面
    function step36HandleProtectionPlusPage() {
        console.log('开始处理Protection Plus页面...');
        waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
            const offerName = document.querySelector('p.offer-name');
            if (offerName && offerName.textContent === 'Before you proceed...') {
                console.log('确认是Protection Plus页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"No, thanks"按钮
                    const noThanksButton = document.querySelector('button#btnSkip');
                    if (noThanksButton) {
                        console.log('找到No, thanks按钮');
                        clickButtonAndWait(noThanksButton, () => {
                            console.log('Protection Plus页面提交完成');
                        });
                    } else {
                        console.log('未找到No, thanks按钮');
                    }
                }, 1000);
            }
        });
    }

    // 步骤37：处理Securely ID页面
    function step37HandleSecurelyIDPage() {
        console.log('开始处理Securely ID页面...');
        waitForElement('div#offerFrame', function(divElement) {
            const offerName = document.querySelector('p.offer-name');
            if (offerName && offerName.textContent === 'Securely ID') {
                console.log('确认是Securely ID页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"No, thanks"按钮
                    const noThanksButton = document.querySelector('button#btnSkip');
                    if (noThanksButton) {
                        console.log('找到No, thanks按钮');
                        clickButtonAndWait(noThanksButton, () => {
                            console.log('Securely ID页面提交完成');
                        });
                    } else {
                        console.log('未找到No, thanks按钮');
                    }
                }, 1000);
            }
        });
    }

    // 步骤38：处理E-file中心第4步页面
    function step38HandleEfileHubStep4Page() {
        console.log('开始处理E-file中心第4步页面...');
        waitForElement('div.efilehub-cards__steppers.last-number', function(steppersDiv) {
            const stepDiv = steppersDiv.querySelector('div.step');
            if (stepDiv && stepDiv.textContent === '4') {
                console.log('确认是E-file中心第4步页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"Sign and File Your Return"链接
                    const signAndFileLink = document.querySelector('a#signandfile-more-button');
                    if (signAndFileLink) {
                        console.log('找到Sign and File Your Return链接');
                        clickButtonAndWait(signAndFileLink, () => {
                            console.log('E-file中心第4步页面提交完成');
                        });
                    } else {
                        console.log('未找到Sign and File Your Return链接');
                    }
                }, 1000);
            }
        });
    }

    // 步骤39：处理签名页面
    function step39HandleSignaturePage() {
        console.log('开始处理签名页面...');
        waitForElement('div.signature-page', function(signatureDiv) {
            console.log('确认是签名页面');
            // 等待页面加载
            setTimeout(() => {
                // 勾选签名复选框
                const signatureCheckbox = document.querySelector('input[type="checkbox"]');
                if (signatureCheckbox && !signatureCheckbox.checked) {
                    console.log('找到签名复选框');
                    signatureCheckbox.click();
                    console.log('签名复选框已勾选');

                    // 点击"Continue"按钮
                    const continueButton = document.querySelector('button.btn-success');
                    if (continueButton) {
                        console.log('找到Continue按钮');
                        clickButtonAndWait(continueButton, () => {
                            console.log('签名页面提交完成');
                        });
                    } else {
                        console.log('未找到Continue按钮');
                    }
                }
            }, 1000);
        });
    }

    // 步骤40：处理支付确认页面
    function step40HandlePaymentConfirmationPage() {
        console.log('开始处理支付确认页面...');
        waitForElement('div.payment-confirmation', function(paymentDiv) {
            console.log('确认是支付确认页面');
            // 等待页面加载
            setTimeout(() => {
                // 点击"Continue"按钮
                const continueButton = document.querySelector('button.btn-success');
                if (continueButton) {
                    console.log('找到Continue按钮');
                    clickButtonAndWait(continueButton, () => {
                        console.log('支付确认页面提交完成');
                    });
                } else {
                    console.log('未找到Continue按钮');
                }
            }, 1000);
        });
    }

    // 步骤41：处理银行账户页面
    function step41HandleBankAccountsPage() {
        console.log('开始处理银行账户页面...');
        waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
            const pageTitle = document.querySelector('h1');
            if (pageTitle && pageTitle.textContent === 'Bank Accounts') {
                console.log('确认是银行账户页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"Continue"按钮
                    const continueButton = document.querySelector('button#btnSubmit');
                    if (continueButton) {
                        console.log('找到Continue按钮');
                        clickButtonAndWait(continueButton, () => {
                            console.log('银行账户页面提交完成');
                        });
                    } else {
                        console.log('未找到Continue按钮');
                    }
                }, 1000);
            }
        });
    }

    // 修改步骤42：处理MD直接存款详情页面
    function step42HandleMDDirectDepositPage() {
        console.log('开始处理MD直接存款详情页面...');

        // 防止重复执行
        if (executedSteps.mdDirectDepositPage) {
            console.log('MD直接存款详情页面已处理过，跳过...');
            return;
        }

        // 获取保存的数据
        const savedData = getData();
        if (savedData.length === 0) {
            console.log('未找到用户数据');
            return;
        }
        const data = savedData[savedData.length - 1];

        // 等待页面加载完成
        waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
            const pageTitle = document.querySelector('h1');
            if (pageTitle && pageTitle.textContent === 'MD Direct Deposit Details') {
                console.log('确认是MD直接存款详情页面');

                // 等待页面完全加载
                setTimeout(() => {
                    try {
                        // 检查是否存在已保存的银行选项
                        const allLabels = document.querySelectorAll('label[class="custom-radio-label"]');
                        let bankFound = false;

                        // 遍历所有label查找匹配的银行名称
                        allLabels.forEach(label => {
                            if (label.textContent.includes(data.bankName)) {
                                console.log('找到匹配的银行:', data.bankName);
                                bankFound = true;

                                // 点击找到的银行选项
                                clickButtonAndWait(label, () => {
                                    console.log('银行选项点击完成');

                                    // 获取并设置对应的radio按钮
                                    const radioId = label.getAttribute('for');
                                    const radio = document.getElementById(radioId);
                                    if (radio) {
                                        radio.checked = true;
                                        radio.dispatchEvent(new Event('change', { bubbles: true }));
                                        console.log('已设置radio按钮状态');
                                    }

                                    // 等待Continue按钮可点击
                                    function checkContinueButton() {
                                        const continueBtn = document.querySelector('button#btnSubmit');
                                        if (continueBtn) {
                                            const isDisabled = continueBtn.disabled ||
                                                             continueBtn.classList.contains('btn-not-ready') ||
                                                             getComputedStyle(continueBtn).opacity === '0.5';

                                            if (!isDisabled) {
                                                console.log('Continue按钮可以点击');
                                                clickButtonAndWait(continueBtn, () => {
                                                    console.log('Continue按钮点击完成');
                                                    executedSteps.mdDirectDepositPage = true;
                                                });
                                            } else {
                                                console.log('Continue按钮还不能点击，等待中...');
                                                setTimeout(checkContinueButton, 500);
                                            }
                                        } else {
                                            setTimeout(checkContinueButton, 500);
                                        }
                                    }

                                    // 开始检查Continue按钮状态
                                    setTimeout(checkContinueButton, 1000);
                                });
                            }
                        });

                        // 如果没有找到匹配的银行，则点击Add a bank account选项
                        if (!bankFound) {
                            console.log('未找到匹配的银行，点击Add a bank account选项');
                            const addBankLabel = document.querySelector('label[for="acctId_addBankRadio"]');
                            if (addBankLabel) {
                                console.log('找到Add a bank account选项');

                                // 点击Add a bank account选项
                                clickButtonAndWait(addBankLabel, () => {
                                    console.log('Add a bank account选项点击完成');

                                    // 获取并设置对应的radio按钮
                                    const radio = document.getElementById('acctId_addBankRadio');
                                    if (radio) {
                                        radio.checked = true;
                                        radio.dispatchEvent(new Event('change', { bubbles: true }));
                                        console.log('已设置radio按钮状态');
                                    }

                                    // 等待Continue按钮可点击
                                    function checkContinueButton() {
                                        const continueBtn = document.querySelector('button#btnSubmit');
                                        if (continueBtn) {
                                            const isDisabled = continueBtn.disabled ||
                                                             continueBtn.classList.contains('btn-not-ready') ||
                                                             getComputedStyle(continueBtn).opacity === '0.5';

                                            if (!isDisabled) {
                                                console.log('Continue按钮可以点击');
                                                clickButtonAndWait(continueBtn, () => {
                                                    console.log('Continue按钮点击完成');

                                                    // 等待银行信息输入页面加载
                                                    setTimeout(() => {
                                                        // 填写银行名称
                                                        const bankNameInput = document.querySelector('input#bankName');
                                                        if (bankNameInput) {
                                                            console.log('找到银行名称输入框');
                                                            simulateTyping(bankNameInput, data.bankName).then(() => {
                                                                // 选择账户类型（Checking）
                                                                const checkingLabel = document.querySelector('label[for="accountType_1Radio"]');
                                                                if (checkingLabel) {
                                                                    console.log('找到Checking账户类型选项');
                                                                    checkingLabel.click();

                                                                    // 填写路由号码
                                                                    const routingInput = document.querySelector('input#routingNumber');
                                                                    if (routingInput) {
                                                                        console.log('找到路由号码输入框');
                                                                        simulateTyping(routingInput, data.routingNumber).then(() => {
                                                                            // 填写账号
                                                                            const accountInput = document.querySelector('input#accountNumber');
                                                                            if (accountInput) {
                                                                                console.log('找到账号输入框');
                                                                                simulateTyping(accountInput, data.accountNumber).then(() => {
                                                                                    // 等待所有输入完成后点击Continue按钮
                                                                                    setTimeout(() => {
                                                                                        const finalContinueBtn = document.querySelector('button#btnSubmit');
                                                                                        if (finalContinueBtn) {
                                                                                            console.log('找到最终Continue按钮');
                                                                                            clickButtonAndWait(finalContinueBtn, () => {
                                                                                                console.log('银行信息填写完成并提交');
                                                                                                executedSteps.mdDirectDepositPage = true;
                                                                                            });
                                                                                        }
                                                                                    }, 2000);
                                                                                });
                                                                            }
                                                                        });
                                                                    }
                                                                }
                                                            });
                                                        }
                                                    }, 2000);
                                                });
                                            } else {
                                                console.log('Continue按钮还不能点击，等待中...');
                                                setTimeout(checkContinueButton, 500);
                                            }
                                        } else {
                                            setTimeout(checkContinueButton, 500);
                                        }
                                    }

                                    // 开始检查Continue按钮状态
                                    setTimeout(checkContinueButton, 1000);
                                });
                            } else {
                                console.log('未找到Add a bank account选项');
                            }
                        }
                    } catch (error) {
                        console.error('处理MD直接存款详情页面时出错:', error);
                    }
                }, 1000);
            }
        });
    }

    // 步骤43：处理ID信息页面
    function step43HandleIDInfoPage() {
        console.log('开始处理ID信息页面...');
        waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
            const pageTitle = document.querySelector('h1');
            if (pageTitle && pageTitle.textContent === 'ID Information') {
                console.log('确认是ID信息页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"I do not wish to provide"复选框
                    const refuseIdLabel = document.querySelector('label[for="taxpayerRefuseId"]');
                    if (refuseIdLabel) {
                        console.log('找到I do not wish to provide复选框');
                        refuseIdLabel.click();

                        // 等待1秒后点击Continue按钮
                        setTimeout(() => {
                            const continueButton = document.querySelector('button#btnSubmit.btn.btn-lg.btn-continue');
                            if (continueButton) {
                                clickButtonAndWait(continueButton, () => {
                                    console.log('ID信息页面提交完成');
                                });
                            } else {
                                console.log('未找到Continue按钮');
                            }
                        }, 1000);
                    } else {
                        console.log('未找到I do not wish to provide复选框');
                    }
                }, 1000);
            }
        });
    }

    // 步骤44：处理AGI访问页面
    function step44HandleAGIAccessPage() {
        console.log('开始处理AGI访问页面...');
        waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
            if (divElement.querySelector('div.access_pin-aw__1GdmB')) {
                console.log('确认是AGI访问页面');
                // 等待页面加载
                setTimeout(() => {
                    // 点击"No"选项
                    const noLabel = document.querySelector('label[for="info.accessChoice_3Radio"]');
                    if (noLabel) {
                        console.log('找到No选项');
                        noLabel.click();

                        // 等待1秒后点击Continue按钮
                        setTimeout(() => {
                            const continueButton = document.querySelector('button#btnSubmit');
                            if (continueButton) {
                                clickButtonAndWait(continueButton, () => {
                                    console.log('AGI访问页面提交完成');
                                });
                            } else {
                                console.log('未找到Continue按钮');
                            }
                        }, 1000);
                    } else {
                        console.log('未找到No选项');
                    }
                }, 1000);
            }
        });
    }

    // 步骤45：处理双重验证页面
    function step45Handle2FAPage() {
        console.log('开始处理双重验证页面...');
        waitForElement('div.two-factor-auth', function(tfaDiv) {
            console.log('确认是双重验证页面');
            // 等待页面加载
            setTimeout(() => {
                // 勾选跳过双重验证复选框
                const bypassCheckbox = document.querySelector('input#bypass-checkbox');
                if (bypassCheckbox && !bypassCheckbox.checked) {
                    console.log('找到跳过双重验证复选框');
                    bypassCheckbox.click();
                    console.log('跳过双重验证复选框已勾选');

                    // 点击"Continue"按钮
                    const continueButton = document.querySelector('button.btn-success');
                    if (continueButton) {
                        console.log('找到Continue按钮');
                        clickButtonAndWait(continueButton, () => {
                            console.log('双重验证页面提交完成');
                        });
                    } else {
                        console.log('未找到Continue按钮');
                    }
                }
            }, 1000);
        });
    }

    // 步骤46：处理准备提交页面
    function step46HandleSubmitPage() {
        console.log('开始处理准备提交页面...');

        // 等待页面加载
        setTimeout(() => {
            try {
                // 选择Yes选项
                console.log('尝试选择Yes选项...');
                const yesLabel = document.querySelector('label[for="efileSelection_1Radio"]');
                if (yesLabel) {
                    // 点击Yes选项
                    forceClick(yesLabel);
                    console.log('点击Yes选项');

                    // 直接操作radio按钮
                    const radio = document.getElementById('efileSelection_1Radio');
                    if (radio) {
                        radio.checked = true;
                        radio.dispatchEvent(new Event('change', { bubbles: true }));
                        console.log('已直接设置radio按钮');
                    }

                    // 点击Continue按钮
                    const continueBtn = document.querySelector('button#btnSubmit');
                    if (continueBtn) {
                        console.log('尝试点击Continue按钮');
                        continueBtn.disabled = false;
                        continueBtn.classList.remove('btn-not-ready');
                        forceClick(continueBtn);
                    } else {
                        console.log('未找到Continue按钮');
                    }
                } else {
                    console.log('未找到Yes选项的label');
                }
            } catch (error) {
                console.error('处理准备提交页面时出错:', error);
            }
        }, 2000);
    }

    // 步骤47：处理最后一步页面
    function step47HandleLastStepPage() {
        console.log('=== 开始处理最后一步页面 ===');

        waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
            const pageTitle = document.querySelector('h1');
            if (pageTitle && pageTitle.textContent === 'Last step!') {
                console.log('✅ 确认是最后一步页面');

                // 创建一个函数来检查按钮状态
                function checkFileNowButton() {
                    const fileNowButton = document.querySelector('button#btnSubmit[title="File Now"]');

                    if (fileNowButton) {
                        console.log('找到File Now按钮，检查状态...');

                        // 检查按钮是否被禁用
                        const isDisabled = fileNowButton.disabled ||
                                         fileNowButton.classList.contains('btn-not-ready') ||
                                         fileNowButton.classList.contains('disabled') ||
                                         getComputedStyle(fileNowButton).opacity === '0.5';

                        if (!isDisabled) {
                            console.log('File Now按钮可以点击，准备点击');

                            // 确保按钮在视图中可见
                            fileNowButton.scrollIntoView({behavior: 'smooth', block: 'center'});

                            // 等待滚动完成后点击
                            setTimeout(() => {
                                clickButtonAndWait(fileNowButton, () => {
                                    console.log('最后一步页面提交完成');
                                });
                            }, 500);
                        } else {
                            console.log('File Now按钮当前不可点击，等待后重试...');
                            setTimeout(checkFileNowButton, 1000); // 每秒检查一次按钮状态
                        }
                    } else {
                        console.log('未找到File Now按钮，等待后重试...');
                        setTimeout(checkFileNowButton, 1000);
                    }
                }

                // 开始检查按钮状态
                setTimeout(checkFileNowButton, 1000);
            }
        });
    }

    // 修改步骤48：处理PIN页面
    function step48HandlePinPage() {
        console.log('=== 开始处理PIN页面 ===');

        try {
            // 立即检查页面元素
            const topElm = document.querySelector('span#topElm[data-build-number]');
            const pinInput = document.querySelector('input#info\\.taxPayerPin');
            const continueBtn = document.querySelector('button#btnSubmit');

            // 输出当前页面状态
            console.log('当前页面状态:', {
                'PIN输入框': pinInput ? '存在' : '不存在',
                'Continue按钮': continueBtn ? '存在' : '不存在'
            });

            // 验证是否是正确的页面
            if (topElm && pinInput) {
                console.log('✅ 确认是PIN页面');

                // 获取保存的数据
                const savedData = getData();
                if (savedData.length === 0) {
                    console.log('未找到用户数据');
                    return;
                }
                const data = savedData[savedData.length - 1];

                // 使用邮编的前5位作为PIN
                const pin = data.zip.substring(0, 5);
                console.log('使用邮编前5位作为PIN:', pin);

                // 模拟输入PIN
                simulateTyping(pinInput, pin).then(() => {
                    console.log('PIN输入完成');

                    // 点击Continue按钮
                    if (continueBtn) {
                        console.log('找到Continue按钮');
                        continueBtn.disabled = false;
                        continueBtn.classList.remove('btn-not-ready');
                        clickButtonAndWait(continueBtn, () => {
                            console.log('Continue按钮点击完成');
                            executedSteps.pinPage = true;
                        });
                    }
                });
            }
        } catch (error) {
            console.error('处理PIN页面时出错:', error);
        }
    }

    // 处理银行账户信息填写页面
    function step42HandleBankAccountInfo() {
        console.log('开始处理银行账户信息填写页面...');

        // 获取保存的数据
        const savedData = getData();
        if (savedData.length === 0) {
            console.log('未找到用户数据');
            return;
        }
        const data = savedData[savedData.length - 1];

        // 等待页面加载完成
        waitForElement('form h1', function(titleElement) {
            if (titleElement.textContent === 'Bank Account Info') {
                console.log('确认是银行账户信息页面');

                // 等待页面完全加载
                setTimeout(() => {
                    try {
                        // 1. 填写账户昵称(银行名称)
                        const bankNameInput = document.querySelector('input[name="accounts[0].bankName"]');
                        if (bankNameInput) {
                            console.log('找到银行名称输入框');
                            simulateTyping(bankNameInput, data.bankName).then(() => {
                                // 2. 选择账户类型(Checking)
                                const accountTypeSelect = document.querySelector('select[name="accounts[0].type"]');
                                if (accountTypeSelect) {
                                    console.log('找到账户类型选择框');
                                    accountTypeSelect.value = 'C'; // C代表Checking
                                    accountTypeSelect.dispatchEvent(new Event('change', { bubbles: true }));

                                    // 3. 填写路由号码
                                    const routingInput = document.querySelector('input[name="accounts[0].rtn"]');
                                    if (routingInput) {
                                        console.log('找到路由号码输入框');
                                        simulateTyping(routingInput, data.routingNumber).then(() => {
                                            // 4. 填写账号
                                            const accountInput = document.querySelector('input[name="accounts[0].acctNo"]');
                                            if (accountInput) {
                                                console.log('找到账号输入框');
                                                simulateTyping(accountInput, data.accountNumber).then(() => {
                                                    // 等待所有输入完成后点击Continue按钮
                                                    setTimeout(() => {
                                                        const continueBtn = document.querySelector('button#btnSubmit');
                                                        if (continueBtn) {
                                                            console.log('找到Continue按钮');
                                                            clickButtonAndWait(continueBtn, () => {
                                                                console.log('银行账户信息填写完成并提交');
                                                            });
                                                        }
                                                    }, 2000);
                                                });
                                            }
                                        });
                                    }
                                }
                            });
                        }
                    } catch (error) {
                        console.error('处理银行账户信息页面时出错:', error);
                    }
                }, 1000);
            }
        });
    }

    // 主函数
    async function init() {
        await waitForCloudflare();

        // 每30秒重置一次步骤状态
        setInterval(resetSteps, 30000);

        // 添加循环检查机制
        function checkPages() {
            // 只在注册页面添加按钮
            waitForElement('#introText', function(introText) {
                if (introText.textContent.includes('Create your account') && !executedSteps.registration) {
                    // 只在注册页面添加导入按钮
                    addImportButton();
                    executedSteps.registration = true;
                }
            });

            // 检查是否在验证码页面
            waitForElement('input#verifyCode', function(verifyInput) {
                if (verifyInput && !executedSteps.verification) {
                    step2HandleVerificationCode();
                    executedSteps.verification = true;
                }
            });

            // 检查是否在验证码页面
            waitForElement('label.control-label.form-label[for="InternalPhoneNumber"]', function(label) {
                if (label.textContent === 'Please enter the verification code we sent to' && !executedSteps.verification) {
                    step2HandleVerificationCode();
                    executedSteps.verification = true;
                }
            });

            // 步骤3：检查是否在创建账户页面
            waitForElement('button#submit', function(submitButton) {
                if (submitButton && submitButton.textContent.includes('Create Account') && !executedSteps.createAccountPage) {
                    step3HandleCreateAccountPage();
                    executedSteps.createAccountPage = true;
                }
            });

            // 步骤4：检查是否在账户确认页面
            waitForElement('div.confirmation-page', function(confirmationDiv) {
                if (confirmationDiv && !executedSteps.accountConfirmationPage) {
                    step4HandleAccountConfirmationPage();
                    executedSteps.accountConfirmationPage = true;
                }
            });

            // 步骤5：检查是否在欢迎页面
            waitForElement('div.welcome-page', function(welcomeDiv) {
                if (welcomeDiv && !executedSteps.welcomePage) {
                    step5HandleWelcomePage();
                    executedSteps.welcomePage = true;
                }
            });

            // 检查是否在纳税人信息页面
            waitForElement('div.col-md-12 h3', function(titleElement) {
                if (titleElement.textContent === 'Taxpayer\'s Information' && !executedSteps.taxpayerInfo) {
                    step6HandleTaxpayerInfo();
                    executedSteps.taxpayerInfo = true;
                }
            });

            // 检查是否在地址信息页面
            waitForElement('label[for="address.line1"]', function(addressLabel) {
                if (addressLabel.textContent.includes('Address (street number & name)') && !executedSteps.addressInfo) {
                    step7HandleAddressInfo();
                    executedSteps.addressInfo = true;
                }
            });

            // 检查是否在同意页面
            waitForElement('h1#page-title', function(titleElement) {
                if (titleElement.textContent === 'We need your approval on a couple things' && !executedSteps.consent) {
                    step13HandleConsent();
                    executedSteps.consent = true;
                }
            });

            // 检查是否在W-2表单页面
            waitForElement('h1[data-testid="page-title-h1"]', function(titleElement) {
                if (titleElement.textContent === 'Wage and tax statement' && !executedSteps.w2Form) {
                    step27HandleW2Form();
                    executedSteps.w2Form = true;
                }
            });

            // 步骤3：检查是否在欢迎页面
            waitForElement('main#congratsTSUser.congratsTSUser', function(mainElement) {
                if (mainElement && !executedSteps.welcomePage) {
                    step3HandleWelcomePage();
                    executedSteps.welcomePage = true;
                }
            });

            // 步骤4：检查是否在套餐选择页面
            waitForElement('div#entryPackageSelection.container', function(divElement) {
                if (divElement && !executedSteps.packageSelection) {
                    step4HandlePackageSelection();
                    executedSteps.packageSelection = true;
                }
            });

            // 步骤5：检查是否在上传1040表格页面
            waitForElement('div#mfe-main-container.mfe-main-container', function(divElement) {
                const pageTitle = document.querySelector('h1[data-testid="page-title-h1"]');
                if (pageTitle && pageTitle.textContent === 'Upload last year\'s tax return (Form 1040)' && !executedSteps.upload1040) {
                    step5HandleUpload1040();
                    executedSteps.upload1040 = true;
                }
            });

            // 步骤8：检查是否在附加信息页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                const pageTitle = document.querySelector('h1[data-testid="page-title-h1"]');
                if (pageTitle && pageTitle.textContent === 'Additional Information' && !executedSteps.additionalInfo) {
                    step8HandleAdditionalInfo();
                    executedSteps.additionalInfo = true;
                }
            });

            // 步骤9：检查是否在报税状态页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                if (executedSteps.filingStatusPage) {
                    return;
                }

                // 检查方式1：通过页面标题
                const pageTitle = document.querySelector('h1');
                const isTitleMatch = pageTitle && pageTitle.textContent === 'What\'s your filing status?';

                // 检查方式2：通过Filing Wizard文字
                const filingWizardDiv = document.querySelector('div#filingWizard');
                const strongText = filingWizardDiv?.querySelector('strong');
                const isWizardMatch = strongText && strongText.textContent === 'Need help determining your filing status?';

                // 如果任一方式匹配，执行处理函数
                if ((isTitleMatch || isWizardMatch) && !executedSteps.filingStatusPage) {
                    console.log('检测到报税状态页面:', {
                        '通过标题匹配': isTitleMatch,
                        '通过Wizard匹配': isWizardMatch
                    });
                    step9HandleFilingStatusPage();
                    executedSteps.filingStatusPage = true;
                }
            });

            // 步骤10：检查是否在受抚养人页面
            waitForElement('div#content-header.content-header', function(divElement) {
                const pageTitle = document.querySelector('h1#page-title');
                if (pageTitle && pageTitle.textContent === 'Dependents or Qualifying Person(s)' && !executedSteps.dependentsPage) {
                    step10HandleDependents();
                    executedSteps.dependentsPage = true;
                }
            });

            // 步骤11：检查是否在身份保护PIN页面
            waitForElement('div#content-header.content-header', function(divElement) {
                const pageTitle = document.querySelector('h1#page-title');
                if (pageTitle && pageTitle.textContent === 'IRS Identity Protection PIN' && !executedSteps.identityPinPage) {
                    step11HandleIdentityPin();
                    executedSteps.identityPinPage = true;
                }
            });

            // 步骤12：检查是否在基本信息摘要页面
            waitForElement('div#page-header', function(divElement) {
                const pageTitle = document.querySelector('h1#page-title');
                if (pageTitle && pageTitle.textContent === 'Basic Information Summary' && !executedSteps.basicInfoSummary) {
                    step12HandleBasicInfoSummary();
                    executedSteps.basicInfoSummary = true;
                }
            });

            // 步骤14：检查是否在开始页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                const pageTitle = document.querySelector('h1[data-testid="page-title-h1"]');
                if (pageTitle && pageTitle.textContent === 'Let\'s do this! #slayit' && !executedSteps.startPage) {
                    step14HandleStartPage();
                    executedSteps.startPage = true;
                }
            });

            // 步骤15：检查是否在快速文件页面
            waitForElement('div#formsInHand-chooseWrapper', function(divElement) {
                if (divElement && !executedSteps.quickFilePage) {
                    step15HandleQuickFilePage();
                    executedSteps.quickFilePage = true;
                }
            });

            // 步骤16：检查是否在导航栏页面
            waitForElement('nav.navbar', function(navElement) {
                const continueLink = navElement.querySelector('a.btn.btn-success');
                if (continueLink && continueLink.textContent.includes('Continue')) {
                    step16HandleNavBarPage();
                    executedSteps.navBarPage = true;
                }
            });

            // 步骤17：检查是否在准备页面
            waitForElement('div#formsInHand-readyWrapper', function(divElement) {
                if (divElement && !executedSteps.readyPage) {
                    step17HandleReadyPage();
                    executedSteps.readyPage = true;
                }
            });

            // 步骤18：检查是否在W2表单选择页面
            waitForElement('button.add-w2', function(addW2Button) {
                step18HandleW2FormSelectPage();
                executedSteps.w2FormSelectPage = true;
            });

            // 步骤19：检查是否在W-2工资声明页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                const pageTitle = document.querySelector('h1[data-testid="page-title-h1"]');
                if (pageTitle && pageTitle.textContent === 'W-2 Wage Statement' && !executedSteps.w2WageStatementPage) {
                    step19HandleW2WageStatementPage();
                    executedSteps.w2WageStatementPage = true;
                }
            });

            // 步骤20：检查是否在其他事项页面
            waitForElement('div#content-header.content-header', function(divElement) {
                const pageTitle = document.querySelector('h1[data-testid="page-title-h1"]');
                if (pageTitle && pageTitle.textContent === 'Anything else?' && !executedSteps.anythingElsePage) {
                    step20HandleAnythingElsePage();
                    executedSteps.anythingElsePage = true;
                }
            });

            // 步骤21：检查是否在平价医疗法案页面
            waitForElement('div#content-header.content-header', function(divElement) {
                const pageTitle = document.querySelector('h1[data-testid="page-title"]');
                if (pageTitle && pageTitle.textContent === 'Affordable Care Act Health Insurance' && !executedSteps.healthCarePage) {
                    step21HandleHealthCarePage();
                    executedSteps.healthCarePage = true;
                }
            });

            // 步骤22：检查是否在州申报开始页面
            waitForElement('div#content-header.content-header', function(divElement) {
                const pageTitle = document.querySelector('h1#page-title');
                if (pageTitle && pageTitle.textContent === 'Let\'s get started on your state return' && !executedSteps.stateReturnPage) {
                    step22HandleStateReturnPage();
                    executedSteps.stateReturnPage = true;
                }
            });

            // 步骤23：检查是否在马里兰州健康保险页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                const stateName = document.querySelector('h1');
                if (stateName && stateName.textContent === 'Maryland Return') {
                    const question = document.querySelector('h3.subsection');
                    if (question && question.textContent === 'Does the taxpayer have health care coverage?' && !executedSteps.marylandHealthCarePage) {
                        step23HandleMarylandHealthCarePage();
                        executedSteps.marylandHealthCarePage = true;
                    }
                }
            });

            // 步骤24：检查是否在马里兰州受抚养人健康保险页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                const stateName = document.querySelector('h1');
                if (stateName && stateName.textContent === 'Maryland Return') {
                    const question = document.querySelector('h3.subsection');
                    if (question && question.textContent === 'Do all the dependents have health care coverage?' && !executedSteps.marylandDependentHealthCarePage) {
                        step24HandleMarylandDependentHealthCarePage();
                        executedSteps.marylandDependentHealthCarePage = true;
                    }
                }
            });

            // 步骤25：检查是否在马里兰州祝贺页面
            waitForElement('div#content-header.content-header', function(divElement) {
                const pageTitle = document.querySelector('h1#page-title');
                if (pageTitle && pageTitle.innerHTML.includes('Congrats!') && !executedSteps.marylandCongratsPage) {
                    step25HandleMarylandCongratsPage();
                    executedSteps.marylandCongratsPage = true;
                }
            });

            // 步骤26：检查是否在州申报网格页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                const pageTitle = document.querySelector('h1');
                if (pageTitle && pageTitle.textContent === 'State Return' && !executedSteps.stateReturnGridPage) {
                    step26HandleStateReturnGridPage();
                    executedSteps.stateReturnGridPage = true;
                }
            });

            // 检查是否在下载申报表页面
            waitForElement('button#print-return-selector-download', function(downloadButton) {
                if (downloadButton && !executedSteps.downloadReturnPage) {
                    step40HandleDownloadReturnPage();
                    executedSteps.downloadReturnPage = true;
                }
            });

            // 检查是否在准备提交页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                const pageTitle = document.querySelector('h1');
                if (pageTitle && pageTitle.textContent === 'Ready to file?' && !executedSteps.readyToFilePage) {
                    step28HandleReadyToFilePage();
                    executedSteps.readyToFilePage = true;
                }
            });

            // 检查是否在E-file中心页面1-4
            waitForElement('div.content-columns__header__column-1', function(headerDiv) {
                const federalDiv = headerDiv.querySelector('div');
                if (federalDiv && federalDiv.textContent === 'Federal') {
                    if (!executedSteps.efileHubPage) {
                        step29HandleEfileHubPage();
                        executedSteps.efileHubPage = true;
                    } else if (!executedSteps.efileHubStep2Page) {
                        step31HandleEfileHubStep2Page();
                        executedSteps.efileHubStep2Page = true;
                    } else if (!executedSteps.efileHubStep3Page) {
                        step34HandleEfileHubStep3Page();
                        executedSteps.efileHubStep3Page = true;
                    } else if (!executedSteps.efileHubStep4Page) {
                        step38HandleEfileHubStep4Page();
                        executedSteps.efileHubStep4Page = true;
                    }
                }
            });

            // 检查是否在联邦退款方式页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                const pageTitle = document.querySelector('h1');
                if (pageTitle && pageTitle.textContent === 'Federal Refund Method' && !executedSteps.federalRefundMethodPage) {
                    step30HandleFederalRefundMethodPage();
                    executedSteps.federalRefundMethodPage = true;
                }
            });

            // 检查是否在E-file中心第2步页面
            waitForElement('div.efilehub-cards__steppers', function(steppersDiv) {
                const stepDiv = steppersDiv.querySelector('div.step');
                if (stepDiv && stepDiv.textContent === '2' && !executedSteps.efileHubStep2Page) {
                    step31HandleEfileHubStep2Page();
                    executedSteps.efileHubStep2Page = true;
                }
            });

            // 检查是否在州退款方式页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                const pageTitle = document.querySelector('h1');
                if (pageTitle && pageTitle.textContent === 'State Refund Method' && !executedSteps.stateRefundMethodPage) {
                    step32HandleStateRefundMethodPage();
                    executedSteps.stateRefundMethodPage = true;
                }
            });

            // 检查是否在马里兰州同意披露页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                const pageTitle = document.querySelector('h1');
                if (pageTitle && pageTitle.textContent === 'Maryland - Online Filing Consent to Disclose and Perjury Statement' && !executedSteps.marylandConsentPage) {
                    step33HandleMarylandConsentPage();
                    executedSteps.marylandConsentPage = true;
                }
            });

            // 检查是否在E-file中心第3步页面
            waitForElement('div.efilehub-cards__steppers', function(steppersDiv) {
                const stepDiv = steppersDiv.querySelector('div.step');
                if (stepDiv && stepDiv.textContent === '3' && !executedSteps.efileHubStep3Page) {
                    step34HandleEfileHubStep3Page();
                    executedSteps.efileHubStep3Page = true;
                }
            });

            // 检查是否在Last Look页面
            waitForElement('main#lastLookOffer', function(mainElement) {
                if (mainElement && !executedSteps.lastLookPage) {
                    step35HandleLastLookPage();
                    executedSteps.lastLookPage = true;
                }
            });

            // 检查是否在Protection Plus页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                const pageTitle = document.querySelector('p.offer-name');
                if (pageTitle && pageTitle.textContent === 'Before you proceed...' && !executedSteps.protectionPlusPage) {
                    step36HandleProtectionPlusPage();
                    executedSteps.protectionPlusPage = true;
                }
            });

            // 检查是否在Securely ID页面
            waitForElement('div#offerFrame', function(divElement) {
                const pageTitle = document.querySelector('p.offer-name');
                if (pageTitle && pageTitle.textContent === 'Securely ID' && !executedSteps.securelyIDPage) {
                    step37HandleSecurelyIDPage();
                    executedSteps.securelyIDPage = true;
                }
            });

            // 检查是否在E-file中心第4步页面
            waitForElement('div.efilehub-cards__steppers.last-number', function(steppersDiv) {
                const stepDiv = steppersDiv.querySelector('div.step');
                if (stepDiv && stepDiv.textContent === '4' && !executedSteps.efileHubStep4Page) {
                    step38HandleEfileHubStep4Page();
                    executedSteps.efileHubStep4Page = true;
                }
            });

            // 检查是否在银行账户页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                const pageTitle = document.querySelector('h1');
                if (pageTitle && pageTitle.textContent === 'Bank Accounts' && !executedSteps.bankAccountsPage) {
                    step41HandleBankAccountsPage();
                    executedSteps.bankAccountsPage = true;
                }
            });

            // 检查是否在MD直接存款详情页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                const pageTitle = document.querySelector('h1');
                if (pageTitle && pageTitle.textContent === 'MD Direct Deposit Details' && !executedSteps.mdDirectDepositPage) {
                    step42HandleMDDirectDepositPage();
                    executedSteps.mdDirectDepositPage = true;
                }
            });

            // 检查是否在ID信息页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                const pageTitle = document.querySelector('h1');
                if (pageTitle && pageTitle.textContent === 'ID Information' && !executedSteps.idInfoPage) {
                    step43HandleIDInfoPage();
                    executedSteps.idInfoPage = true;
                }
            });

            // 检查是否在AGI访问页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                if (divElement.querySelector('div.access_pin-aw__1GdmB')) {
                    step44HandleAGIAccessPage();
                    executedSteps.agiaAccessPage = true;
                }
            });

            // 检查是否在双重验证页面
            waitForElement('div.two-factor-auth', function(tfaDiv) {
                step45Handle2FAPage();
                executedSteps.twoFactorAuth = true;
            });

            // 检查是否在提交页面
            waitForElement('div#fileTaxWrapper', function(divElement) {
                if (divElement && divElement.querySelector('p') && divElement.querySelector('p').textContent === 'You\'re all set to file!' && !executedSteps.submitPage) {
                    console.log('检测到提交页面，准备执行step46HandleSubmitPage');
                    setTimeout(() => {
                        step46HandleSubmitPage();
                        executedSteps.submitPage = true;
                    }, 1000);
                }
            });

            // 另一种检查方式
            waitForElement('label[for="efileSelection_1Radio"]', function(yesLabel) {
                if (yesLabel && yesLabel.textContent.includes('Yes, I\'m done with my return(s) and ready to file!')) {
                    console.log('检测到提交页面的Yes选项，准备执行step46HandleSubmitPage');
                    setTimeout(() => {
                        step46HandleSubmitPage();
                        executedSteps.submitPage = true;
                    }, 1000);
                }
            });

            // 检查是否在最后一步页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                const pageTitle = document.querySelector('h1');
                if (pageTitle && pageTitle.textContent === 'Last step!' && !executedSteps.lastStepPage) {
                    step47HandleLastStepPage();
                    executedSteps.lastStepPage = true;
                }
            });

            // 检查是否在PIN页面
            waitForElement('span#topElm[data-build-number]', function(spanElement) {
                step48HandlePinPage();
                executedSteps.pinPage = true;
            });

            // 在checkPages函数中添加以下检查
            // 检查是否在PIN页面
            waitForElement('span#topElm[data-build-number]', function(spanElement) {
                if (spanElement && !executedSteps.pinPage) {
                    step48HandlePinPage();
                    executedSteps.pinPage = true;
                }
            });

            // 检查是否在银行账户信息页面
            waitForElement('form h1', function(titleElement) {
                if (titleElement.textContent === 'Bank Account Info' && !executedSteps.bankAccountInfo) {
                    step42HandleBankAccountInfo();
                    executedSteps.bankAccountInfo = true;
                }
            });

            // 检查是否在马里兰州县区选择页面
            waitForElement('div#root.App_mfe-content__2dJSY', function(divElement) {
                const pageTitle = document.querySelector('h1');
                if (pageTitle && pageTitle.textContent === 'Maryland Return' && !executedSteps.marylandCountyPage) {
                    const label = document.querySelector('label[for="8qngjbfqTS"]');
                    if (label && label.textContent.includes('Select the County')) {
                        step23HandleMarylandCountyPage();
                    }
                }
            });

            // 每5秒重新检查一次
            setTimeout(checkPages, 5000);
        }

        // 开始循环检查
        checkPages();
    }

    // 步骤1：填写注册表单
    function step1FillRegistrationForm() {
        const savedData = getData();
        if (savedData.length === 0) return;

        const data = savedData[savedData.length - 1];

        waitForElement('#Email', function(emailInput) {
            simulateTyping(emailInput, data.email);

            waitForElement('#UserName', function(usernameInput) {
                simulateTyping(usernameInput, data.username);

                waitForElement('#Password', function(passwordInput) {
                    waitForElement('#ConfirmPassword', function(confirmPasswordInput) {
                        simulateTyping(passwordInput, data.password);
                        setTimeout(() => {
                            simulateTyping(confirmPasswordInput, data.password);

                            waitForElement('#PhoneNumber', function(phoneInput) {
                                simulateTyping(phoneInput, data.phone);
                                setTimeout(() => {
                                    clickCreateAccountButton();
                                }, 500);
                            });
                        }, 500);
                    });
                });
            });
        });
    }

    // 步骤2：处理验证码
    function step2HandleVerificationCode() {
        console.log('开始处理验证码...');

        // 获取保存的数据
        const savedData = getData();
        if (savedData.length === 0) {
            console.log('未找到用户数据');
            return;
        }
        const data = savedData[savedData.length - 1];

        // 等待3秒后开始获取验证码
        setTimeout(() => {
            console.log('开始获取验证码...');
            // 从导入的手机链接中获取验证码
            if (data.phoneLink) {
                console.log('使用导入的手机链接:', data.phoneLink);

                // 使用GM_xmlhttpRequest在后台获取验证码
                GM_xmlhttpRequest({
                    method: 'GET',
                    url: data.phoneLink,
                    onload: function(response) {
                        console.log('获取到响应:', response.responseText);
                        // 从响应中提取6位数字验证码
                        const codeMatch = response.responseText.match(/\b\d{6}\b/);
                        if (codeMatch) {
                            const code = codeMatch[0];
                            console.log('提取到验证码:', code);

                            // 填写验证码
                            const codeInput = document.querySelector('#Code');
                            if (codeInput) {
                                simulateTyping(codeInput, code).then(() => {
                                    // 点击验证按钮
                                    const verifyButton = document.querySelector('#VerifyPhone_Verify');
                                    if (verifyButton) {
                                        verifyButton.click();
                                        console.log('验证按钮已点击');
                                    }
                                });
                            }
                        } else {
                            console.log('未找到6位数字验证码');
                        }
                    },
                    onerror: function(error) {
                        console.error('获取验证码失败:', error);
                    }
                });
            } else {
                console.log('未找到手机链接');
            }
        }, 3000);
    }

    // 点击创建账户按钮
    function clickCreateAccountButton() {
        waitForElement('.ca-register-button', function(buttonContainer) {
            waitForElement('#create-account', function(button) {
                const checkButtonState = setInterval(() => {
                    if (!button.disabled) {
                        clearInterval(checkButtonState);
                        button.click();
                    }
                }, 100);
            });
        });
    }

    // 保存数据到本地存储
    function saveData(data) {
        let savedData = JSON.parse(localStorage.getItem('taxslayerData') || '[]');
        savedData.push(data);
        localStorage.setItem('taxslayerData', JSON.stringify(savedData));
        return savedData;
    }

    // 从本地存储获取数据
    function getData() {
        return JSON.parse(localStorage.getItem('taxslayerData') || '[]');
    }

    // 删除本地存储的数据
    function deleteData(index) {
        let savedData = getData();
        savedData.splice(index, 1);
        localStorage.setItem('taxslayerData', JSON.stringify(savedData));
        return savedData;
    }

    // 添加导入按钮和表单
    function addImportButton() {
        // 检查是否已存在按钮，避免重复添加
        if (document.querySelector('.cyber-btn')) {
            return;
        }

        // 创建样式
        const style = document.createElement('style');
        style.textContent = `
            .button-group {
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 999999;
                display: flex;
                gap: 10px;
            }

            .cyber-btn {
                background: #0066ff;
                border: none;
                color: white;
                padding: 6px 16px;
                cursor: pointer;
                border-radius: 25px;
                font-family: Arial, sans-serif;
                font-size: 14px;
                display: flex;
                align-items: center;
                gap: 5px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                height: 32px;
                min-width: 100px;
                justify-content: center;
                position: relative;
            }

            .cyber-btn::before {
                content: "🚀";
                font-size: 14px;
                margin-right: 2px;
            }

            .cyber-btn::after {
                content: "杀手半自动";
                position: absolute;
                bottom: -8px;  /* 改为-8px，让标签更靠近按钮 */
                right: -10px;  /* 改为right，并设置为-10px使其偏右 */
                transform: none;  /* 移除transform */
                left: auto;  /* 移除left */
                background: #ff3366;
                color: white;
                padding: 1px 6px;
                border-radius: 10px;
                font-size: 10px;
                white-space: nowrap;
                line-height: 16px;
                height: 16px;
            }

            .cyber-btn:hover {
                filter: brightness(0.9);
            }

            .import-form {
                position: fixed;
                top: 70px;
                left: 20px;
                z-index: 999998;
                background: white;
                padding: 20px;
                border-radius: 8px;
                border: 1px solid #ddd;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                display: none;
                width: 800px;
                max-height: 80vh;
                overflow-y: auto;
            }

            .textarea-container {
                margin: 15px 0;
            }

            .textarea-container textarea {
                width: 100%;
                height: 100px;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                line-height: 1.5;
            }

            .close-btn {
                position: absolute;
                top: 10px;
                right: 10px;
                background: #ff3366;
                border: none;
                color: white;
                width: 24px;
                height: 24px;
                border-radius: 12px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
            }

            .save-btn {
                background: #00cc66;
                margin: 20px auto 0;
                width: 200px;
                height: 40px;
                font-size: 16px;
            }

            .save-btn::before, .save-btn::after {
                display: none;
            }
        `;
        document.head.appendChild(style);

        // 创建按钮组容器
        const buttonGroup = document.createElement('div');
        buttonGroup.className = 'button-group';
        document.body.appendChild(buttonGroup);

        // 创建导入按钮
        const importButton = document.createElement('button');
        importButton.className = 'cyber-btn';
        importButton.textContent = '资料填放';
        buttonGroup.appendChild(importButton);  // 将按钮添加到按钮组中

        // 创建导入表单
        const importForm = document.createElement('div');
        importForm.className = 'import-form';
        importForm.innerHTML = `
            <button class="close-btn">×</button>
            <div>请按以下格式输入数据(用Tab键分隔):</div>
            <pre>邮箱    用户名    密码    手机号    手机验证链接    名字    姓氏    社会安全号    出生日期    地址    邮编    雇主EIN    雇主公司    雇主地址    雇主邮编    金额1    金额2    金额3    金额4    金额5    金额6    金额16    金额17    银行名称    路由号码    账号</pre>
            <div style="color: #ff9d00; margin-top: 10px;">注意: 手机验证链接必须完整,例如: https://example.com/verify/123456</div>
            <div class="textarea-container">
                <textarea placeholder="在此粘贴数据..."></textarea>
            </div>
            <button class="cyber-btn save-btn">保存并开始</button>
        `;
        document.body.appendChild(importForm);

        // 导入按钮点击事件
        importButton.onclick = function() {
            importForm.style.display = importForm.style.display === 'none' ? 'block' : 'none';
        };

        // 保存按钮点击事件
        importForm.querySelector('.save-btn').onclick = function() {
            const textarea = importForm.querySelector('textarea');
            const text = textarea.value.trim();
            if (!text) {
                alert('请输入数据！');
                return;
            }

            const rows = text.split('\n');
            let importSuccess = false;
            rows.forEach(row => {
                const [
                    email, username, password, phone, phoneLink,
                    firstName, lastName, ssn, dob, address, zip,
                    employerEin, employerName, employerAddress, employerZip,
                    amount1, amount2, amount3, amount4, amount5, amount6,
                    amount16, amount17, bankName, routingNumber, accountNumber
                ] = row.split('\t').map(item => item.trim());

                if (email && username && password && phone && phoneLink) {
                    saveData({
                        email, username, password, phone, phoneLink,
                        firstName, lastName, ssn, dob, address, zip,
                        employerEin, employerName, employerAddress, employerZip,
                        amount1, amount2, amount3, amount4, amount5, amount6,
                        amount16, amount17, bankName, routingNumber, accountNumber
                    });
                    importSuccess = true;
                }
            });

            if (importSuccess) {
                textarea.value = '';
                // 移除弹窗提示，直接关闭表单
                importForm.style.display = 'none';
                // 直接开始执行填写表单
                step1FillRegistrationForm();
            } else {
                alert('数据格式错误,请检查后重试！');
            }
        };

        // 关闭按钮点击事件
        importForm.querySelector('.close-btn').onclick = function() {
            importForm.style.display = 'none';
        };
    }

    // 获取保存的数据
    const savedData = JSON.parse(localStorage.getItem('taxslayerData') || '[]');
    if (savedData.length > 0) {
        const data = savedData[savedData.length - 1];
        console.log('手机验证链接:', data.phoneLink);
        // 如果想看完整数据
        console.log('完整数据:', data);
    }

    // 等待页面加载完成
    function waitForPageLoad() {
        if (document.readyState === 'complete') {
            console.log('页面加载完成');
            init();
        } else {
            window.addEventListener('load', function() {
                console.log('页面加载完成');
                init();
            });
        }
    }

    // 启动脚本
    waitForPageLoad();

    // 步骤3：处理欢迎页面
    function step3HandleWelcomePage() {
        console.log('开始处理欢迎页面...');

        // 确保页面完全加载
        waitForPageLoad();

        console.log('欢迎页面元素情况:');
        console.log('- 标题:', document.title);
        console.log('- H1元素:', Array.from(document.querySelectorAll('h1')).map(h => h.textContent));
        console.log('- 按钮数量:', document.querySelectorAll('button').length);
        console.log('- 链接数量:', document.querySelectorAll('a').length);

        // 确保等待足够长时间后再寻找按钮
        setTimeout(() => {
            // 查找所有可能的继续按钮（广泛查找）
            const continueSelectors = [
                // 按钮元素
                'button.continue-button',
                'button.btn-success',
                'button.btn-primary',
                'button:contains("Continue")',
                'button:contains("继续")',
                'button:contains("Next")',
                'button.next-step',
                'button[title="Continue"]',
                'button#btnSubmit',
                // 链接元素
                'a.btn-success',
                'a.btn-primary',
                'a:contains("Continue")',
                'a:contains("继续")',
                'a:contains("Next")',
                'a.btn:contains("Continue")',
                // 其他可能元素
                '.continue-button',
                '.next-button',
                '[data-action="continue"]'
            ];

            console.log('尝试查找继续按钮...');

            // 首先检查所有按钮，看是否有明显的继续按钮
            const allButtons = document.querySelectorAll('button, a.btn');
            console.log('页面所有按钮文本:');
            Array.from(allButtons).forEach((btn, i) => {
                console.log(`- 按钮 ${i+1}: ${btn.textContent.trim()}, 类名: ${btn.className}`);
            });

            // 尝试所有选择器
            let continueButton = null;
            for (const selector of continueSelectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        // 优先使用第一个可见的按钮
                        for (const el of elements) {
                            const style = window.getComputedStyle(el);
                            if (style.display !== 'none' && style.visibility !== 'hidden' && el.offsetParent !== null) {
                                continueButton = el;
                                console.log(`找到可见的继续按钮: ${selector}, 文本: ${el.textContent.trim()}`);
                                break;
                            }
                        }

                        if (continueButton) break;

                        // 如果没找到可见按钮，使用第一个
                        continueButton = elements[0];
                        console.log(`找到继续按钮: ${selector}, 文本: ${elements[0].textContent.trim()}`);
                        break;
                    }
                } catch (e) {
                    console.log(`选择器 ${selector} 查找错误:`, e);
                }
            }

            // 如果没找到，尝试根据文本内容查找
            if (!continueButton) {
                const allClickables = document.querySelectorAll('button, a, input[type="submit"]');
                for (const el of allClickables) {
                    const text = el.textContent.toLowerCase().trim();
                    if (text.includes('continue') || text.includes('next') || text.includes('go') ||
                        text.includes('proceed') || text.includes('submit') || text === '下一步' ||
                        text === '继续' || text === '提交') {
                        continueButton = el;
                        console.log(`根据文本内容找到可能的继续按钮: ${el.tagName}, 文本: ${text}`);
                        break;
                    }
                }
            }

            // 如果还是没找到，尝试通用按钮
            if (!continueButton) {
                const anyPrimaryButton = document.querySelector('.btn-primary, .primary-button, button.btn:last-child');
                if (anyPrimaryButton) {
                    continueButton = anyPrimaryButton;
                    console.log('未找到明确的继续按钮，使用主要按钮:', anyPrimaryButton.textContent.trim());
                }
            }

            if (continueButton) {
                console.log('找到继续按钮，准备点击. 按钮详情:',
                           `类型=${continueButton.tagName}`,
                           `ID=${continueButton.id}`,
                           `类名=${continueButton.className}`,
                           `文本=${continueButton.textContent.trim()}`);

                // 确保按钮可见并在视口内
                continueButton.scrollIntoView({behavior: 'smooth', block: 'center'});

                // 等待滚动完成后再点击
                setTimeout(() => {
                    console.log('尝试点击继续按钮');

                    // 先尝试直接点击
                    try {
                        continueButton.click();
                        console.log('直接点击完成');
                    } catch (e) {
                        console.log('直接点击失败:', e);
                    }

                    // 再使用forceClick进行多种方式点击
                    setTimeout(() => {
                        console.log('使用forceClick再次尝试');
                        forceClick(continueButton);

                        // 如果是链接，考虑直接导航
                        if (continueButton.tagName === 'A' && continueButton.href) {
                            setTimeout(() => {
                                console.log('通过href直接导航:', continueButton.href);
                                window.location.href = continueButton.href;
                            }, 500);
                        }

                        console.log('欢迎页面继续按钮已尝试所有点击方法');
                    }, 500);
                }, 800);
            } else {
                console.log('未找到任何继续按钮，尝试查找表单提交按钮或其他可点击元素');

                // 尝试查找表单提交按钮
                const submitButton = document.querySelector('form button[type="submit"], input[type="submit"]');
                if (submitButton) {
                    console.log('找到表单提交按钮:', submitButton.textContent || submitButton.value);
                    forceClick(submitButton);
                } else {
                    // 尝试找到页面上任何主要按钮
                    const anyButton = document.querySelector('button.btn, a.btn');
                    if (anyButton) {
                        console.log('找到替代按钮:', anyButton.textContent);
                        forceClick(anyButton);
                    } else {
                        console.log('无法找到任何可点击按钮，尝试查找可能的下一步链接');

                        // 如果没有按钮，尝试查找可能的链接
                        const possibleLink = document.querySelector('a[href*="next"], a[href*="continue"]');
                        if (possibleLink) {
                            console.log('找到可能的链接:', possibleLink.href);
                            window.location.href = possibleLink.href;
                        } else {
                            console.log('未找到任何可操作元素，欢迎页面处理失败');
                        }
                    }
                }
            }
        }, 2000); // 延长等待时间确保页面完全加载
    }

    // 步骤4：处理套餐选择页面
    function step4HandlePackageSelection() {
        console.log('开始处理套餐选择页面...');

        // 确保页面完全加载
        waitForPageLoad();

        console.log('套餐选择页面元素情况:');
        console.log('- 标题:', document.title);
        console.log('- H1元素:', Array.from(document.querySelectorAll('h1')).map(h => h.textContent));

        // 延长等待时间，确保页面元素完全加载
        setTimeout(() => {
            // 首先尝试找到用户指定的精确元素
            const freeLinkDiv = document.querySelector('div#chooseFreeLink.btn-skip');

            if (freeLinkDiv) {
                console.log('找到精确的Free套餐按钮元素: div#chooseFreeLink');
                console.log('按钮文本:', freeLinkDiv.textContent.trim());

                // 确保元素在视图中可见
                freeLinkDiv.scrollIntoView({behavior: 'smooth', block: 'center'});

                // 等待滚动完成后点击
                setTimeout(() => {
                    console.log('尝试点击Free套餐按钮');

                    // 尝试多种点击方法
                    try {
                        // 1. 原生点击
                        freeLinkDiv.click();
                        console.log('原生点击完成');
                    } catch (e) {
                        console.log('原生点击失败:', e);
                    }

                    // 2. 使用MouseEvent
                    setTimeout(() => {
                        try {
                            const clickEvent = new MouseEvent('click', {
                                view: window,
                                bubbles: true,
                                cancelable: true
                            });
                            freeLinkDiv.dispatchEvent(clickEvent);
                            console.log('事件点击完成');
                        } catch (e) {
                            console.log('事件点击失败:', e);
                        }

                        // 3. 使用forceClick
                        setTimeout(() => {
                            console.log('使用forceClick再次尝试');
                            forceClick(freeLinkDiv);
                            console.log('Free套餐按钮已被点击');
                        }, 500);
                    }, 500);
                }, 1000);

                return;
            }

            // 如果没有找到精确元素，尝试通过内容查找
            console.log('未找到精确的Free套餐按钮，尝试通过内容查找');

            // 检查所有可能的按钮元素
            const allButtons = document.querySelectorAll('div[role="button"], button, a.btn, [tabindex="0"]');
            console.log(`找到 ${allButtons.length} 个可能的按钮元素`);

            // 记录所有按钮的内容，帮助排查
            Array.from(allButtons).forEach((btn, i) => {
                console.log(`- 按钮 ${i+1}: ${btn.textContent.trim()}, ID=${btn.id}, 类名=${btn.className}`);
            });

            // 尝试按文本内容查找
            let freeButton = null;

            for (const btn of allButtons) {
                const text = btn.textContent.toLowerCase();
                if (text.includes('free') || text.includes('simply free') ||
                    text.includes('no thanks') || text.includes('continue with simply')) {
                    freeButton = btn;
                    console.log('按内容找到Free套餐按钮:', text);
                    break;
                }
            }

            // 如果找到了按钮，点击它
            if (freeButton) {
                console.log('找到Free套餐按钮，准备点击');

                // 确保元素可见
                freeButton.scrollIntoView({behavior: 'smooth', block: 'center'});

                // 等待滚动完成后点击
                setTimeout(() => {
                    forceClick(freeButton);
                    console.log('Free套餐按钮已被点击');
                }, 1000);
            } else {
                console.log('未找到任何Free套餐按钮，尝试查找任何可能的继续按钮');

                // 尝试查找任何继续按钮
                const continueSelectors = [
                    'button:contains("Continue")',
                    'a:contains("Continue")',
                    'div[role="button"]:contains("Continue")',
                    'button.btn-primary',
                    'a.btn-primary',
                    'button.continue',
                    'a.continue'
                ];

                for (const selector of continueSelectors) {
                    try {
                        const continueButton = document.querySelector(selector);
                        if (continueButton) {
                            console.log(`找到继续按钮: ${selector}`);
                            continueButton.scrollIntoView({behavior: 'smooth', block: 'center'});
                            setTimeout(() => {
                                forceClick(continueButton);
                                console.log('继续按钮已被点击');
                            }, 1000);
                            return;
                        }
                    } catch (e) {
                        console.log(`选择器 ${selector} 查找错误:`, e);
                    }
                }

                console.log('未找到任何可点击按钮，尝试点击页面上最可能的按钮');

                // 使用视觉上最可能是主按钮的元素
                const anyButton = document.querySelector('.btn, button, a.btn, [role="button"]');
                if (anyButton) {
                    console.log('找到替代按钮:', anyButton.textContent);
                    forceClick(anyButton);
                } else {
                    console.log('找不到任何可点击元素，步骤4失败');
                }
            }
        }, 3000); // 等待3秒确保页面完全加载
    }

    // 步骤5：处理上传1040表格页面
    function step5HandleUpload1040() {
        console.log('开始处理上传1040表格页面...');
        waitForElement('div#mfe-main-container.mfe-main-container', function(divElement) {
            const pageTitle = document.querySelector('h1[data-testid="page-title-h1"]');
            if (pageTitle && pageTitle.textContent === 'Upload last year\'s tax return (Form 1040)') {
                console.log('确认是上传1040表格页面');

                // 等待页面加载
                setTimeout(() => {
                    // 查找Skip按钮
                    const skipButton = document.querySelector('a#f1040-pdfimport-btnSkip, a.btn-skip, a.btnSecondary___SVlxh');
                    if (skipButton) {
                        console.log('找到Skip按钮');
                        forceClick(skipButton);
                        console.log('上传1040表格页面Skip按钮已点击');
                        return; // 点击Skip后就不需要执行下面的代码了
                    } else {
                        console.log('未找到Skip按钮，尝试第一次使用选项...');
                    }

                    // 选择"这是我第一次使用TaxSlayer"选项
                    const firstTimeOption = document.querySelector('input[type="radio"][value="firstTime"], label:contains("first time")');
                    if (firstTimeOption) {
                        console.log('找到"第一次使用"选项');
                        forceClick(firstTimeOption);
                        console.log('"第一次使用"选项已选择');
                    } else {
                        console.log('未找到"第一次使用"选项');
                    }

                    // 点击"Continue"按钮
                    setTimeout(() => {
                        const continueButton = document.querySelector('button.continue-button, button.btn-success, button#btnSubmit');
                        if (continueButton) {
                            console.log('找到Continue按钮');
                            forceClick(continueButton);
                            console.log('上传1040表格页面继续按钮已点击');
                        } else {
                            console.log('未找到Continue按钮');
                        }
                    }, 1000);
                }, 1000);
            }
        });
    }

    // 检查是否在提交页面
    const fileTaxWrapper = document.querySelector('div#fileTaxWrapper');
    const yesLabel = document.querySelector('label[for="efileSelection_1Radio"]');

    if ((fileTaxWrapper && fileTaxWrapper.querySelector('p')?.textContent === 'You\'re all set to file!') ||
        (yesLabel && yesLabel.textContent.includes('Yes, I\'m done with my return(s) and ready to file!'))) {
        console.log('在提交页面，准备点击...');

        // 点击Yes选项
        if (yesLabel) {
            yesLabel.click();

            // 等待1秒后点击Continue按钮
            setTimeout(() => {
                const continueBtn = document.querySelector('button#btnSubmit');
                if (continueBtn) {
                    continueBtn.disabled = false;
                    continueBtn.classList.remove('btn-not-ready');
                    continueBtn.click();
                }
            }, 1000);
        }
    }

})();